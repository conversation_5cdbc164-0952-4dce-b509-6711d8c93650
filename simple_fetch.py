#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Token Holders数据获取脚本
"""

import requests
import time

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 请求头 - 添加更多浏览器头信息
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.oklink.com/',
        'Origin': 'https://www.oklink.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        # 尝试添加可能的API密钥头
        'Ok-Access-Key': '',  # 如果有API密钥，在这里填入
        'X-API-Key': '',      # 备用API密钥头
    }
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取数据...")
    
    while True:
        # 构建请求参数
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': int(time.time() * 1000)
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页...")
            
            # 发送请求
            response = requests.get(base_url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 检查响应状态
            if data.get('code') != 0:
                print(f"❌ API错误: {data.get('msg', '未知错误')}")
                break
            
            hits = data.get('data', {}).get('hits', [])
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 写入文件
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('tokenContractAddress', '')
                    value_change = item.get('valueChange24h', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已处理 {len(hits)} 条记录，累计 {total_processed} 条")
            
            # 如果返回的记录数少于limit，说明是最后一页
            if len(hits) < limit:
                break
            
            # 准备下一页
            offset += limit
            is_first_write = False
            
            # 延迟1秒，避免请求过快
            time.sleep(1)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            break
    
    print(f"🎉 完成！总共处理了 {total_processed} 条记录")
    print(f"📁 数据已保存到: {output_file}")

if __name__ == "__main__":
    fetch_token_holders()
