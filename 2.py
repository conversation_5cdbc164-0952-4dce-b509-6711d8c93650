import hashlib
import base64

# 输入你的密码，需替换为实际密码
password = "0x041fe35780cacf18368229b27bee31afaf074817f6cebf9d37ae080843df095832ba0b64080a2a6b5600505ca40c9d129d9cb345bce578230644a5861b1661f033".encode('utf-8')
# 解码盐值，你提供的盐值是Base64编码，需先解码
salt_b64 = "RiwfJaYkgHYpn3uLlxIl0zjSRDzgf/ouyvK32GVe4Jw="
salt = base64.b64decode(salt_b64)
# 迭代次数
iterations = 625740
# 哈希函数
hash_algorithm ='sha256'
# 输出密钥长度，这里设置为32字节（256位），可根据实际情况调整
key_length = 32

derived_key = hashlib.pbkdf2_hmac(
    hash_algorithm,
    password,
    salt,
    iterations,
    dklen=key_length
)
print(derived_key.hex())