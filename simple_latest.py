#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单版本 - 使用最新认证信息
"""

import requests
import time

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 最新认证信息
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': 'LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjQ4MTIzODE2NDM=',
        'x-cdn': 'https://static.oklink.com',
        'x-id-group': '2920162119777970001-c-28',
        'x-locale': 'zh_CN',
        'x-simulated-trading': 'undefined',
        'x-site-info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        'x-utc': '8',
        'x-zkdex-env': '0',
        'ok-verify-token': '25ab133e-e6a8-4516-82cf-d68239e1380a',
        'ok-verify-sign': 'X/oHQ+ahOdtd5vgegZcg6+q/HmJbxl4sjgMHVdAeWb0=',  # 最新签名
        'Referer': 'https://www.oklink.com/zh-hans/x-layer/token/0x4cf55a735f45271548faed60340ef4658ccb167c?tab=holders'
    }
    
    cookies = {
        'devId': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'locale': 'zh_CN',
        'fingerprint_id': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'traceId': '2930162120255470001',
        'ok-ses-id': '6W/4YsgDpbRLLt0qRQc4XEld3kSK5t6nBS485Hy3n2jaH8ouRF/Q01q6t4/cMoApQmjB+oTXMquRwCixyGn+4CsT4vlknZidXiCFkO2zzcMaNOQ/5i8N4vdGEHULKKcK',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取数据...")
    
    while True:
        # 动态时间戳
        current_timestamp = int(time.time() * 1000)
        
        # 更新时间戳（但保持原签名，看看是否有效）
        session.headers['ok-timestamp'] = str(current_timestamp)
        
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 第 {offset // limit + 1} 页 (offset: {offset}, timestamp: {current_timestamp})...")
            
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应: {response.text[:200]}")
                
                if response.status_code in [401, 403]:
                    print("⚠️ 认证失败！签名可能已过期")
                    print("💡 请重新获取最新的fetch请求信息")
                break
            
            data = response.json()
            
            if data.get('code') != 0:
                print(f"❌ API错误: {data.get('msg', '未知错误')}")
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📊 本页: {len(hits)} 条, 总计: {total_count} 条")
            
            if not hits:
                break
            
            # 保存数据 - 使用正确的字段名
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    holder_address = item.get('holderAddress', '')
                    value = item.get('value', 0)
                    f.write(f"{holder_address},{value}\n")
            
            total_processed += len(hits)
            print(f"✅ 累计: {total_processed} 条")
            
            if len(hits) < limit:
                break
            
            offset += limit
            is_first_write = False
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            break
    
    print(f"\n🎉 完成！获取了 {total_processed} 条记录")
    print(f"📁 保存到: {output_file}")

if __name__ == "__main__":
    fetch_token_holders()
