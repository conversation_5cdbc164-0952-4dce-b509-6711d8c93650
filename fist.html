<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> Presale</title>
    <script src="https://cdn.jsdelivr.net/npm/web3@1.10.0/dist/web3.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
        margin: 0;
        position: relative;
        overflow-x: hidden;
      }

      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 20% 80%,
            rgba(120, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(120, 219, 255, 0.2) 0%,
            transparent 50%
          );
        pointer-events: none;
        z-index: -1;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* PC端布局 */
      .main-content {
        display: grid;
        grid-template-columns: 1fr 420px;
        gap: 40px;
        align-items: start;
      }

      /* 品牌标题 */
      .brand-title {
        text-align: center;
        margin-bottom: 40px;
      }

      .brand-logo {
        display: inline-flex;
        align-items: center;
        gap: 20px;
        font-size: 48px;
        font-weight: 900;
        color: white;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 15px;
      }

      .brand-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .brand-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        letter-spacing: 2px;
        text-transform: uppercase;
      }

      .card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        padding: 30px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        color: white;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
      }

      .card-title {
        font-size: 28px;
        font-weight: 800;
        margin-bottom: 30px;
        background: linear-gradient(135deg, #fff, #e0e6ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
      }

      .wallet-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
      }

      .info-item {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1),
          rgba(255, 255, 255, 0.05)
        );
        padding: 20px;
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .info-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transition: left 0.5s ease;
      }

      .info-item:hover::before {
        left: 100%;
      }

      .info-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .info-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 600;
      }

      .info-value {
        font-size: 18px;
        font-weight: 700;
        font-family: monospace;
        color: #fff;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .progress-section {
        margin-bottom: 30px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.08),
          rgba(255, 255, 255, 0.03)
        );
        padding: 25px;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.15);
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .progress-label {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
      }

      .progress-value {
        font-size: 16px;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .progress-bar {
        width: 100%;
        height: 12px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        overflow: hidden;
        margin-bottom: 10px;
        position: relative;
        box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2);
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #ff6b6b, #ee5a24, #ff9ff3);
        border-radius: 25px;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
      }

      .progress-fill::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      .progress-percentage {
        text-align: right;
        font-size: 14px;
        color: #ff6b6b;
        font-weight: 700;
        text-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
      }

      .buy-section {
        margin-bottom: 30px;
      }

      .amount-input {
        display: flex;
        align-items: center;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1),
          rgba(255, 255, 255, 0.05)
        );
        border-radius: 20px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }

      .amount-btn {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.2),
          rgba(255, 255, 255, 0.1)
        );
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        width: 45px;
        height: 45px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 18px;
        font-weight: 700;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .amount-btn:hover {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.3),
          rgba(255, 255, 255, 0.2)
        );
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .amount-display {
        flex: 1;
        text-align: center;
        font-size: 28px;
        font-weight: 800;
        margin: 0 20px;
        color: #fff;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        font-family: monospace;
      }

      .max-btn {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border: none;
        color: white;
        padding: 10px 16px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);
      }

      .max-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);
      }

      .buy-btn {
        width: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 18px;
        border-radius: 18px;
        font-size: 18px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
      }

      .buy-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      .buy-btn:hover::before {
        left: 100%;
      }

      .buy-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
      }

      .buy-btn:disabled {
        background: linear-gradient(135deg, #666, #555);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .buy-btn:disabled::before {
        display: none;
      }

      .limits-info {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
        margin-top: 15px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .purchased-amount {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1),
          rgba(255, 255, 255, 0.05)
        );
        padding: 25px;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
      }

      .purchased-amount::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: shimmer 3s infinite;
      }

      .purchased-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 600;
      }

      .purchased-value {
        font-size: 32px;
        font-weight: 800;
        background: linear-gradient(135deg, #00d4aa, #00b894);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);
      }

      /* 连接钱包按钮 */
      .wallet-section {
        margin-bottom: 30px;
        text-align: center;
      }

      .connect-wallet {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 20px;
        font-weight: 700;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);
        position: relative;
        overflow: hidden;
      }

      .connect-wallet::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      .connect-wallet:hover::before {
        left: 100%;
      }

      .connect-wallet:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(238, 90, 36, 0.4);
      }

      .wallet-display {
        background: rgba(255, 255, 255, 0.1);
        padding: 12px 20px;
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-family: monospace;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
      }

      .invite-section {
        margin-top: 25px;
      }

      .invite-info {
        background: rgba(255, 255, 255, 0.05);
        padding: 15px;
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .invite-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 8px;
      }

      .invite-link-container {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .invite-link-input {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-family: monospace;
      }

      .invite-link-input::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      .copy-btn {
        background: linear-gradient(45deg, #00d4aa, #00b894);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        font-size: 12px;
      }

      .copy-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
      }

      .copy-btn:disabled {
        background: #666;
        cursor: not-allowed;
        transform: none;
      }

      .social-section {
        margin-top: 25px;
      }

      .social-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
      }

      .social-buttons {
        display: flex;
        gap: 12px;
      }

      .social-btn {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 10px 16px;
        border-radius: 8px;
        text-decoration: none;
        text-align: center;
        font-weight: bold;
        transition: all 0.3s ease;
        font-size: 14px;
      }

      .social-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      /* 移动端响应式设计 */
      @media (max-width: 768px) {
        body {
          padding: 15px 0;
        }

        .container {
          padding: 0 15px;
        }

        .main-content {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .brand-title {
          margin-bottom: 30px;
        }

        .brand-logo {
          font-size: 32px;
          gap: 15px;
        }

        .brand-icon {
          width: 55px;
          height: 55px;
          font-size: 26px;
        }

        .brand-subtitle {
          font-size: 14px;
        }

        .card {
          padding: 20px;
          border-radius: 18px;
        }

        .card-title {
          font-size: 24px;
          margin-bottom: 20px;
        }

        .wallet-info {
          grid-template-columns: 1fr;
          gap: 15px;
          margin-bottom: 20px;
        }

        .info-item {
          padding: 15px;
        }

        .info-value {
          font-size: 16px;
        }

        .progress-section {
          padding: 20px;
          margin-bottom: 20px;
        }

        .progress-header {
          flex-direction: column;
          gap: 8px;
          text-align: center;
          margin-bottom: 12px;
        }

        .progress-label,
        .progress-value {
          font-size: 14px;
        }

        .amount-input {
          padding: 12px;
          margin-bottom: 15px;
        }

        .amount-btn {
          width: 40px;
          height: 40px;
          font-size: 16px;
        }

        .amount-display {
          font-size: 24px;
          margin: 0 15px;
        }

        .max-btn {
          padding: 8px 12px;
          font-size: 11px;
        }

        .buy-btn {
          padding: 16px;
          font-size: 16px;
        }

        .purchased-amount {
          padding: 20px;
          margin-bottom: 20px;
        }

        .purchased-value {
          font-size: 28px;
        }

        .social-buttons {
          flex-direction: column;
          gap: 10px;
        }

        .social-btn {
          padding: 12px 16px;
          font-size: 14px;
        }

        .invite-link-container {
          flex-direction: column;
          gap: 10px;
        }

        .invite-link-input {
          font-size: 12px;
          padding: 10px 12px;
        }

        .copy-btn {
          padding: 10px 16px;
          font-size: 12px;
        }

        .limits-info {
          font-size: 12px;
          padding: 12px;
        }
      }

      /* 超小屏幕优化 */
      @media (max-width: 480px) {
        .container {
          padding: 0 10px;
        }

        .brand-logo {
          font-size: 26px;
          gap: 10px;
        }

        .brand-icon {
          width: 45px;
          height: 45px;
          font-size: 22px;
        }

        .card {
          padding: 15px;
        }

        .card-title {
          font-size: 20px;
        }

        .amount-display {
          font-size: 20px;
          margin: 0 12px;
        }

        .purchased-value {
          font-size: 24px;
        }
      }

      .toast {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        z-index: 10000;
        max-width: 280px;
        min-width: 100px;
        word-wrap: break-word;
        white-space: pre-line;
        opacity: 0;
        transition: all 0.3s ease;
        pointer-events: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
      }

      .toast.show {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }

      .toast.success {
        background: rgba(76, 175, 80, 0.9);
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
      }

      .toast.error {
        background: rgba(244, 67, 54, 0.9);
        box-shadow: 0 4px 20px rgba(244, 67, 54, 0.3);
      }

      .toast.warning {
        background: rgba(255, 152, 0, 0.9);
        box-shadow: 0 4px 20px rgba(255, 152, 0, 0.3);
      }

      @media (max-width: 768px) {
        .toast {
          max-width: 250px;
          font-size: 13px;
          padding: 12px 16px;
        }
      }
    </style>
  </head>
  <body>
    <div class="mainnet-banner">*** X LAYER MAINNET - Using Real OKB ***</div>
    <div class="container">
      <div class="header">
        <div class="logo">
          <div class="logo-icon">
            <img
              style="width: 100%; height: 100%; border-radius: 50%"
              src="data:image/jpeg;base64,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"
            />
          </div>
          Cook Presale
          <span style="color: #00d4aa; font-size: 14px">(X Layer Mainnet)</span>
        </div>
        <div class="wallet-address" id="walletAddress">
          <button class="connect-wallet" onclick="connectWallet()">
            Connect Wallet
          </button>
        </div>
      </div>

      <div class="card">
        <h2 class="card-title">Join the Presale</h2>

        <div class="wallet-info">
          <div class="info-item">
            <div class="info-label">Wallet</div>
            <div class="info-value" id="walletDisplay">Not Connected</div>
          </div>
          <div class="info-item">
            <div class="info-label">OKB Balance</div>
            <div class="info-value" id="okbBalance">0.0</div>
          </div>
        </div>

        <div class="progress-section">
          <div class="progress-header">
            <span class="progress-label">Presale Progress</span>
            <span class="progress-value" id="progressValue">0 / 200 OKB</span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              id="progressFill"
              style="width: 0%"
            ></div>
          </div>
          <div class="progress-percentage" id="progressPercentage">0%</div>
        </div>

        <div class="buy-section">
          <div class="amount-input">
            <button class="amount-btn" onclick="decreaseAmount()">-</button>
            <div class="amount-display" id="amountDisplay">0.1</div>
            <button class="amount-btn" onclick="increaseAmount()">+</button>
            <button class="max-btn" onclick="setMaxAmount()">MAX</button>
          </div>

          <button class="buy-btn" id="buyBtn" onclick="buyTokens()" disabled>
            Buy
          </button>

          <div class="limits-info">
            Personal Limit: 2.0 OKB, Total Supply: 200 OKB (X Layer Mainnet)
          </div>
        </div>
      </div>

      <div class="card stats-card">
        <h2 class="card-title">Your Stats</h2>

        <div class="purchased-amount">
          <div class="purchased-label">Your Purchased</div>
          <div class="purchased-value" id="purchasedAmount">0.0 OKB</div>
        </div>

        <div class="invite-section">
          <h3 class="social-title">
            Two-Level Rewards (Parent-Child 3% Parent-Grandchild 1%)
          </h3>
          <div class="invite-info">
            <div class="invite-label">Referral Link</div>
            <div class="invite-link-container">
              <input
                type="text"
                id="inviteLink"
                class="invite-link-input"
                readonly
                placeholder="Connect wallet to generate referral link"
              />
              <button
                class="copy-btn"
                id="copyBtn"
                onclick="copyInviteLink()"
                disabled
              >
                Copy
              </button>
            </div>
          </div>
        </div>

        <div class="social-section">
          <h3 class="social-title">Social</h3>
          <div class="social-buttons">
            <a
              href="https://x.com/Colbcsxyer"
              target="_blank"
              class="social-btn"
              >Twitter</a
            >
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentAmount = 0.1;
      let walletConnected = false;
      let userBalance = 0;
      let totalSold = 0;
      let userPurchased = 0;
      let connectedWalletAddress = "";
      let referrerAddress = "";

      const MIN_AMOUNT = 0.1;
      const MAX_AMOUNT = 2.0;
      const TOTAL_SUPPLY = 200;
      const PROGRESS_WALLET = "******************************************";
      const DEFAULT_REFERRER = "******************************************";
      const bskTokenAddress = "******************************************";
      const bskTokenABI = [
        {
          inputs: [
            { internalType: "address", name: "parent", type: "address" },
          ],
          name: "buyTokens",
          outputs: [],
          stateMutability: "payable",
          type: "function",
        },
        {
          inputs: [],
          name: "saleActive",
          outputs: [{ internalType: "bool", name: "", type: "bool" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [{ internalType: "address", name: "", type: "address" }],
          name: "walletSpent",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          stateMutability: "view",
          type: "function",
        },
        {
          inputs: [],
          name: "totalEthReceived",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          stateMutability: "view",
          type: "function",
        },
      ];

      const OKB_CHAIN_ID = "0xc4";
      const OKB_CHAIN_CONFIG = {
        chainId: OKB_CHAIN_ID,
        chainName: "X Layer Mainnet",
        nativeCurrency: {
          name: "OKB",
          symbol: "OKB",
          decimals: 18,
        },
        rpcUrls: ["https://rpc.xlayer.tech"],
        blockExplorerUrls: ["https://www.okx.com/web3/explorer/xlayer"],
      };

      function getMainnetConfig() {
        return {
          chainId: "0xc4",
          chainName: "X Layer Mainnet",
          nativeCurrency: {
            name: "OKB",
            symbol: "OKB",
            decimals: 18,
          },
          rpcUrls: ["https://rpc.xlayer.tech"],
          blockExplorerUrls: ["https://www.okx.com/web3/explorer/xlayer"],
        };
      }

      async function forceMainnetSwitch() {
        try {
          console.log("Force switching to X Layer Mainnet (196)...");

          await window.ethereum.request({
            method: "wallet_switchEthereumChain",
            params: [{ chainId: "0xc4" }],
          });

          return true;
        } catch (error) {
          console.log("Direct switch failed, trying to add X Layer Mainnet...");
          try {
            await window.ethereum.request({
              method: "wallet_addEthereumChain",
              params: [getMainnetConfig()],
            });
            return true;
          } catch (addError) {
            console.error("Failed to add X Layer Mainnet:", addError);
            return false;
          }
        }
      }

      function updateDisplay() {
        document.getElementById("amountDisplay").textContent =
          currentAmount.toFixed(1);
        document.getElementById("buyBtn").disabled =
          !walletConnected ||
          currentAmount < MIN_AMOUNT ||
          currentAmount > MAX_AMOUNT;

        const progress = (totalSold / TOTAL_SUPPLY) * 100;
        document.getElementById("progressFill").style.width = progress + "%";
        document.getElementById(
          "progressValue"
        ).textContent = `${totalSold.toFixed(1)} / ${TOTAL_SUPPLY} OKB`;
        document.getElementById("progressPercentage").textContent =
          progress.toFixed(1) + "%";
      }

      function increaseAmount() {
        if (currentAmount < MAX_AMOUNT) {
          currentAmount = Math.min(currentAmount + 0.1, MAX_AMOUNT);
          currentAmount = Math.round(currentAmount * 10) / 10;
          updateDisplay();
        }
      }

      function decreaseAmount() {
        if (currentAmount > MIN_AMOUNT) {
          currentAmount = Math.max(currentAmount - 0.1, MIN_AMOUNT);
          currentAmount = Math.round(currentAmount * 10) / 10;
          updateDisplay();
        }
      }

      function setMaxAmount() {
        currentAmount = MAX_AMOUNT;
        updateDisplay();
      }

      async function switchToOKBChain() {
        try {
          console.log(
            "Attempting to switch to X Layer Mainnet (Chain ID: 196)"
          );

          await window.ethereum.request({
            method: "wallet_switchEthereumChain",
            params: [{ chainId: "0xc4" }],
          });

          console.log("Successfully switched to X Layer Mainnet");
          return true;
        } catch (switchError) {
          console.log("Switch error:", switchError);

          if (switchError.code === 4902) {
            try {
              console.log("Adding X Layer Mainnet to MetaMask...");
              const mainnetConfig = getMainnetConfig();
              console.log("X Layer Mainnet config:", mainnetConfig);

              await window.ethereum.request({
                method: "wallet_addEthereumChain",
                params: [mainnetConfig],
              });

              console.log("Successfully added and switched to X Layer Mainnet");
              return true;
            } catch (addError) {
              console.error("Failed to add X Layer Mainnet:", addError);
              return false;
            }
          } else {
            console.error("Failed to switch to X Layer Mainnet:", switchError);
            return false;
          }
        }
      }

      async function getCurrentChainId() {
        try {
          const chainId = await window.ethereum.request({
            method: "eth_chainId",
          });
          return chainId;
        } catch (error) {
          console.error("Failed to get chain ID:", error);
          return null;
        }
      }

      function isCorrectChain(currentChainId) {
        if (!currentChainId) return false;
        const normalizedCurrent = currentChainId.toLowerCase();
        const normalizedExpected = OKB_CHAIN_ID.toLowerCase();
        const decimalCurrent = parseInt(normalizedCurrent, 16);
        const decimalExpected = parseInt(normalizedExpected, 16);

        console.log("Chain comparison:", {
          current: normalizedCurrent,
          expected: normalizedExpected,
          currentDecimal: decimalCurrent,
          expectedDecimal: decimalExpected,
        });

        return (
          normalizedCurrent === normalizedExpected ||
          decimalCurrent === decimalExpected
        );
      }

      function getPurchasedAmount(walletAddress) {
        if (!walletAddress) return 0;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        const stored = localStorage.getItem(key);
        return stored ? parseFloat(stored) : 0;
      }

      function savePurchasedAmount(walletAddress, amount) {
        if (!walletAddress) return;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        const currentAmount = getPurchasedAmount(walletAddress);
        const newAmount = currentAmount + amount;
        localStorage.setItem(key, newAmount.toString());
        return newAmount;
      }

      function updatePurchasedDisplay() {
        if (connectedWalletAddress) {
          userPurchased = getPurchasedAmount(connectedWalletAddress);
          document.getElementById("purchasedAmount").textContent =
            userPurchased.toFixed(1) + " OKB";
        }
      }

      function clearPurchaseHistory(walletAddress) {
        if (!walletAddress) return;
        const key = `purchased_${walletAddress.toLowerCase()}`;
        localStorage.removeItem(key);
        updatePurchasedDisplay();
        console.log("Purchase history cleared for:", walletAddress);
      }
      function showToast(message, type = "default", duration = 3000) {
        const existingToast = document.querySelector(".toast");
        if (existingToast) {
          existingToast.classList.remove("show");
          setTimeout(() => {
            if (existingToast.parentNode) {
              existingToast.parentNode.removeChild(existingToast);
            }
          }, 300);
        }

        const toast = document.createElement("div");
        toast.className = `toast ${type}`;
        toast.textContent = message;

        toast.style.transform = "translate(-50%, -50%) scale(0.8)";

        document.body.appendChild(toast);

        setTimeout(() => {
          toast.classList.add("show");
        }, 50);
        setTimeout(() => {
          toast.classList.remove("show");
          toast.style.transform = "translate(-50%, -50%) scale(0.8)";
          setTimeout(() => {
            if (toast.parentNode) {
              toast.parentNode.removeChild(toast);
            }
          }, 300);
        }, duration);
      }

      function showSuccess(message, duration = 3000) {
        showToast(message, "success", duration);
      }

      function showError(message, duration = 3000) {
        showToast(message, "error", duration);
      }

      function showWarning(message, duration = 3000) {
        showToast(message, "warning", duration);
      }
      function getReferrerFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const referrer = urlParams.get("a");
        if (referrer && referrer.match(/^0x[a-fA-F0-9]{40}$/)) {
          return referrer;
        }
        return "";
      }

      function generateInviteLink(walletAddress) {
        if (!walletAddress) return "";
        const baseUrl = window.location.origin + window.location.pathname;
        return `${baseUrl}?a=${walletAddress}`;
      }
      function updateInviteLink() {
        const inviteLinkInput = document.getElementById("inviteLink");
        const copyBtn = document.getElementById("copyBtn");

        if (connectedWalletAddress) {
          const inviteLink = generateInviteLink(connectedWalletAddress);
          inviteLinkInput.value = inviteLink;
          copyBtn.disabled = false;
        } else {
          inviteLinkInput.value = "";
          inviteLinkInput.placeholder =
            "Connect wallet to generate referral link";
          copyBtn.disabled = true;
        }
      }

      function copyInviteLink() {
        const inviteLinkInput = document.getElementById("inviteLink");
        if (inviteLinkInput.value) {
          inviteLinkInput.select();
          inviteLinkInput.setSelectionRange(0, 99999);

          try {
            document.execCommand("copy");
            showSuccess("Referral link copied to clipboard!", 2000);
          } catch (err) {
            navigator.clipboard
              .writeText(inviteLinkInput.value)
              .then(() => {
                showSuccess("Referral link copied to clipboard!", 2000);
              })
              .catch(() => {
                showError("Copy failed, please copy manually", 2000);
              });
          }
        }
      }

      async function ensureMainnetConnection() {
        try {
          const currentChainId = await getCurrentChainId();
          console.log(
            "Checking chain connection. Current:",
            currentChainId,
            "Expected: 0xc4 (196 - X Layer Mainnet)"
          );

          if (currentChainId !== "0xc4") {
            showToast("Switching to X Layer Mainnet...", "default", 2000);

            const switched = await forceMainnetSwitch();
            if (switched) {
              await new Promise((resolve) => setTimeout(resolve, 1000));
              const newChainId = await getCurrentChainId();
              if (newChainId === "0xc4") {
                showSuccess("Successfully switched to X Layer Mainnet!", 2000);
              } else {
                throw new Error(
                  `Switch failed. Current: ${newChainId}, Expected: 0xc4 (X Layer Mainnet)`
                );
              }
            } else {
              throw new Error("Failed to switch to X Layer Mainnet");
            }
          } else {
            console.log("Already on X Layer Mainnet");
          }
        } catch (error) {
          console.error("Error ensuring X Layer Mainnet connection:", error);
          showWarning(
            "Please manually switch to X Layer Mainnet (Chain ID: 196)",
            4000
          );
          throw error;
        }
      }

      async function checkAndSwitchToMainnet() {
        try {
          const currentChainId = await getCurrentChainId();
          if (currentChainId && !isCorrectChain(currentChainId)) {
            console.log(
              "Detected non-X Layer Mainnet chain, attempting to switch..."
            );
            showToast(
              "Detected wrong network. Switching to X Layer Mainnet...",
              "warning",
              3000
            );

            const switched = await switchToOKBChain();
            if (switched) {
              showSuccess("Switched to X Layer Mainnet!", 2000);
            }
          }
        } catch (error) {
          console.log(
            "Could not auto-switch chain (wallet may not be connected)"
          );
        }
      }

      async function connectWallet() {
        if (typeof window.ethereum !== "undefined") {
          try {
            const accounts = await window.ethereum.request({
              method: "eth_requestAccounts",
            });

            await ensureMainnetConnection();
            const currentChainId = await getCurrentChainId();
            console.log(
              "Current Chain ID:",
              currentChainId,
              "Expected:",
              OKB_CHAIN_ID,
              "(X Layer Mainnet)"
            );
            if (!isCorrectChain(currentChainId)) {
              showError(
                "Failed to switch to X Layer Mainnet. Please switch manually."
              );
              return;
            }

            const account = accounts[0];
            walletConnected = true;
            connectedWalletAddress = account;

            const shortAddress =
              account.slice(0, 6) + "..." + account.slice(-4);
            document.getElementById("walletAddress").innerHTML = shortAddress;
            document.getElementById("walletDisplay").textContent = shortAddress;

            updatePurchasedDisplay();

            updateInviteLink();
            await updateBalance();
            updateDisplay();
          } catch (error) {
            console.error("Failed to connect wallet:", error);
            showError("Failed to connect wallet");
          }
        } else {
          showError("Please install MetaMask or another Web3 wallet");
        }
      }

      async function getWalletBalance(address) {
        try {
          if (typeof window.ethereum !== "undefined") {
            const currentChainId = await getCurrentChainId();
            if (!isCorrectChain(currentChainId)) {
              console.log("Not on X Layer Mainnet, switching...");
              const switched = await switchToOKBChain();
              if (!switched) {
                console.error("Failed to switch to X Layer Mainnet");
                return 0;
              }
            }

            const balance = await window.ethereum.request({
              method: "eth_getBalance",
              params: [address, "latest"],
            });

            const balanceInOKB = parseInt(balance, 16) / Math.pow(10, 18);
            return balanceInOKB;
          } else {
            return Math.random() * 50;
          }
        } catch (error) {
          console.error("Error getting wallet balance:", error);
          return Math.random() * 50;
        }
      }

      async function updateBalance() {
        try {
          if (walletConnected && window.ethereum) {
            const accounts = await window.ethereum.request({
              method: "eth_accounts",
            });
            if (accounts.length > 0) {
              userBalance = await getWalletBalance(accounts[0]);
              document.getElementById("okbBalance").textContent =
                userBalance.toFixed(1);
            }
          }

          totalSold = await getWalletBalance(PROGRESS_WALLET);

          updatePurchasedDisplay();
        } catch (error) {
          console.error("Error updating balance:", error);
          userBalance = 0;
          totalSold = 0;
        }
      }

      async function buyTokens() {
        if (!walletConnected) {
          showWarning("Please connect your wallet first");
          return;
        }

        if (currentAmount < MIN_AMOUNT || currentAmount > MAX_AMOUNT) {
          showWarning(
            `Amount must be between ${MIN_AMOUNT} and ${MAX_AMOUNT} OKB`
          );
          return;
        }

        try {
          await ensureMainnetConnection();

          const finalChainId = await getCurrentChainId();
          console.log(
            "Final chain check before payment:",
            finalChainId,
            "(X Layer Mainnet)"
          );

          if (finalChainId !== "0xc4") {
            throw new Error(
              `Still on wrong network: ${finalChainId}, expected: 0xc4 (X Layer Mainnet)`
            );
          }

          showToast(
            "Confirmed on X Layer Mainnet. Processing payment...",
            "success",
            2000
          );
        } catch (error) {
          console.error("X Layer Mainnet verification failed:", error);
          showError(
            "Cannot proceed without X Layer Mainnet connection. Please switch manually."
          );
          return;
        }

        try {
          document.getElementById("buyBtn").textContent = "Processing...";
          document.getElementById("buyBtn").disabled = true;

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          const currentAccount = accounts[0];
          const currentBalance = await getWalletBalance(currentAccount);

          console.log(`Current account: ${currentAccount}`);
          console.log(`Current balance: ${currentBalance} OKB`);
          console.log(`Trying to buy: ${currentAmount} OKB`);

          const parentAddress = referrerAddress || DEFAULT_REFERRER;
          console.log(`Parent address: ${parentAddress}`);

          if (!window.ethereum) {
            throw new Error("MetaMask not detected");
          }

          const web3 = new Web3(window.ethereum);

          if (!web3.utils.isAddress(parentAddress)) {
            throw new Error(`Invalid parent address: ${parentAddress}`);
          }
          const contract = new web3.eth.Contract(bskTokenABI, bskTokenAddress);

          const amountInWei = web3.utils.toWei(
            currentAmount.toString(),
            "ether"
          );
          console.log(`Converting ${currentAmount} OKB to ${amountInWei} wei`);

          console.log(`Contract address: ${bskTokenAddress}`);
          console.log(
            `Amount in wei: ${amountInWei}, Parent: ${parentAddress}`
          );
          console.log(`Current balance: ${currentBalance} OKB`);
          console.log(`Required amount: ${currentAmount} OKB`);

          const contractCode = await web3.eth.getCode(bskTokenAddress);
          if (contractCode === "0x") {
            throw new Error("Contract not found at the specified address");
          }
          console.log("Contract exists, proceeding with transaction...");

          const currentChainId = await web3.eth.getChainId();
          console.log(`Current chain ID: ${currentChainId} (should be 196)`);

          if (currentChainId !== 196) {
            throw new Error(
              `Wrong network! Current: ${currentChainId}, Expected: 196 (X Layer Mainnet)`
            );
          }

          if (currentBalance < currentAmount) {
            throw new Error(
              `Insufficient balance. You have ${currentBalance.toFixed(
                3
              )} OKB but need ${currentAmount} OKB`
            );
          }

          try {
            const saleActive = await contract.methods.saleActive().call();
            console.log(`Sale active: ${saleActive}`);

            if (!saleActive) {
              throw new Error("Sale is not active");
            }
          } catch (readError) {
            console.log("Cannot read sale status:", readError.message);
          }

          try {
            const walletSpent = await contract.methods
              .walletSpent(currentAccount)
              .call();
            console.log(
              `Wallet already spent: ${web3.utils.fromWei(
                walletSpent,
                "ether"
              )} OKB`
            );

            const totalEthReceived = await contract.methods
              .totalEthReceived()
              .call();
            console.log(
              `Total ETH received by contract: ${web3.utils.fromWei(
                totalEthReceived,
                "ether"
              )} OKB`
            );
          } catch (readError) {
            console.log("Cannot read wallet/contract data:", readError.message);
          }

          try {
            const minPurchase = await contract.methods.MIN_PURCHASE().call();
            const maxPurchase = await contract.methods.MAX_PURCHASE().call();
            console.log(
              `Min purchase: ${web3.utils.fromWei(minPurchase, "ether")} OKB`
            );
            console.log(
              `Max purchase: ${web3.utils.fromWei(maxPurchase, "ether")} OKB`
            );

            if (web3.utils.toBN(amountInWei).lt(web3.utils.toBN(minPurchase))) {
              throw new Error(
                `Purchase amount too small. Minimum: ${web3.utils.fromWei(
                  minPurchase,
                  "ether"
                )} OKB`
              );
            }

            if (web3.utils.toBN(amountInWei).gt(web3.utils.toBN(maxPurchase))) {
              throw new Error(
                `Purchase amount too large. Maximum: ${web3.utils.fromWei(
                  maxPurchase,
                  "ether"
                )} OKB`
              );
            }
          } catch (limitError) {
            console.log("Cannot read purchase limits:", limitError.message);
          }

          console.log(`Calling buyTokens with parent: ${parentAddress}`);
          console.log(
            `Value being sent: ${amountInWei} wei (${currentAmount} OKB)`
          );
          console.log(`Account balance: ${currentBalance} OKB`);
          console.log(`Network: X Layer Mainnet (196)`);

          const gasEstimate = await contract.methods
            .buyTokens(parentAddress)
            .estimateGas({
              from: currentAccount,
              value: amountInWei,
            });

          console.log(`Gas estimate successful: ${gasEstimate}`);

          const txHash = await contract.methods.buyTokens(parentAddress).send({
            from: currentAccount,
            value: amountInWei,
            gas: Math.floor(gasEstimate * 1.2),
          });

          console.log("Contract transaction sent:", txHash.transactionHash);

          const finalTxHash = txHash.transactionHash || txHash;
          showSuccess(
            `Transaction sent!\nHash: ${finalTxHash}\nPlease wait for confirmation.`,
            4000
          );

          const newTotalPurchased = savePurchasedAmount(
            connectedWalletAddress,
            currentAmount
          );
          console.log(
            `Saved purchase: ${currentAmount} OKB, Total: ${newTotalPurchased} OKB`
          );

          updatePurchasedDisplay();
          await updateBalance();

          document.getElementById("buyBtn").textContent = "Buy";
          document.getElementById("buyBtn").disabled = false;
          updateDisplay();

          showSuccess(
            `Successfully purchased ${currentAmount} OKB!\nTransaction: ${finalTxHash}\nReferrer: ${parentAddress}\nTotal purchased: ${newTotalPurchased.toFixed(
              1
            )} OKB`,
            5000
          );
        } catch (error) {
          console.error("Transaction failed:", error);
          console.error("Error details:", {
            code: error.code,
            message: error.message,
            data: error.data,
          });

          let errorMessage = "Transaction failed";
          if (error.code === 4001) {
            errorMessage = "Transaction rejected by user";
          } else if (error.code === -32000) {
            errorMessage =
              "Contract execution failed. Please check: 1) Sufficient balance 2) Contract parameters 3) Network connection";
          } else if (error.code === -32603) {
            errorMessage = "Insufficient funds or network error";
          } else if (
            error.message &&
            error.message.includes("insufficient funds")
          ) {
            errorMessage = `Insufficient funds. You need at least ${currentAmount} OKB + gas fees`;
          } else if (error.message) {
            errorMessage = `Transaction failed: ${error.message}`;
          }

          showError(errorMessage, 5000);
          document.getElementById("buyBtn").textContent = "Buy";
          document.getElementById("buyBtn").disabled = false;
        }
      }

      updateDisplay();
      updatePurchasedDisplay();

      referrerAddress = getReferrerFromURL();
      if (referrerAddress) {
        console.log("Referrer found in URL:", referrerAddress);
        showSuccess(
          `Accessed via referral link, referrer: ${referrerAddress.slice(
            0,
            6
          )}...${referrerAddress.slice(-4)}`,
          3000
        );
      }

      updateBalance();
      if (typeof window.ethereum !== "undefined") {
        window.ethereum.request({ method: "eth_accounts" }).then((accounts) => {
          if (accounts.length > 0) {
            connectWallet();
          } else {
            checkAndSwitchToMainnet();
          }
        });
      }

      setInterval(updateBalance, 30000);
      if (typeof window.ethereum !== "undefined") {
        window.ethereum.on("chainChanged", (chainId) => {
          console.log("Chain changed to:", chainId);

          if (!isCorrectChain(chainId)) {
            showWarning(
              "Wrong network detected! Please use X Layer Mainnet (Chain ID: 196).",
              4000
            );
            setTimeout(() => {
              checkAndSwitchToMainnet();
            }, 2000);
          } else {
            showSuccess("Connected to X Layer Mainnet!", 2000);
            if (walletConnected) {
              updateBalance();
            }
          }
        });

        window.ethereum.on("accountsChanged", (accounts) => {
          if (accounts.length === 0) {
            walletConnected = false;
            connectedWalletAddress = "";
            document.getElementById("walletAddress").innerHTML =
              '<button class="connect-wallet" onclick="connectWallet()">Connect Wallet</button>';
            document.getElementById("walletDisplay").textContent =
              "Not Connected";
            document.getElementById("okbBalance").textContent = "0.0";
            updatePurchasedDisplay();
            updateInviteLink();
          } else if (accounts[0] !== connectedWalletAddress) {
            connectWallet();
          }
        });
      }
    </script>
  </body>
</html>
