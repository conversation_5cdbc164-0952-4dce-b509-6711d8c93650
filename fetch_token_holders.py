#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token Holders API 数据获取脚本
从 OKLink API 获取代币持有者数据并保存到文件
"""

import requests
import json
import time
import sys
from typing import Dict, List, Optional

class TokenHoldersFetcher:
    def __init__(self):
        self.base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
        self.output_file = "1.txt"
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器请求
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.oklink.com/',
        })
    
    def fetch_page(self, offset: int = 0, limit: int = 100) -> Optional[Dict]:
        """
        获取指定页面的数据
        
        Args:
            offset: 偏移量
            limit: 每页数量
            
        Returns:
            API响应数据或None（如果请求失败）
        """
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': int(time.time() * 1000)  # 当前时间戳
        }
        
        try:
            print(f"🔄 正在请求第 {offset // limit + 1} 页数据 (offset: {offset})...")
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 0:
                print(f"✅ 第 {offset // limit + 1} 页请求成功，获取到 {len(data.get('data', {}).get('hits', []))} 条记录")
                return data
            else:
                print(f"❌ API返回错误: {data.get('msg', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
    
    def parse_and_save_data(self, data: Dict, is_first_page: bool = False) -> int:
        """
        解析数据并保存到文件
        
        Args:
            data: API响应数据
            is_first_page: 是否为第一页（决定文件写入模式）
            
        Returns:
            处理的记录数量
        """
        hits = data.get('data', {}).get('hits', [])
        
        if not hits:
            print("⚠️ 没有找到数据记录")
            return 0
        
        # 决定文件写入模式
        mode = 'w' if is_first_page else 'a'
        
        try:
            with open(self.output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_contract_address = item.get('tokenContractAddress', '')
                    value_change_24h = item.get('valueChange24h', 0)
                    
                    # 写入格式：tokenContractAddress,valueChange24h
                    line = f"{token_contract_address},{value_change_24h}\n"
                    f.write(line)
            
            print(f"💾 已保存 {len(hits)} 条记录到 {self.output_file}")
            return len(hits)
            
        except IOError as e:
            print(f"❌ 文件写入失败: {e}")
            return 0
    
    def fetch_all_data(self):
        """
        获取所有页面的数据
        """
        print("🚀 开始获取Token持有者数据...")
        
        offset = 0
        limit = 100
        total_records = 0
        processed_records = 0
        is_first_page = True
        
        while True:
            # 获取当前页数据
            data = self.fetch_page(offset, limit)
            
            if not data:
                print("❌ 获取数据失败，停止处理")
                break
            
            # 获取总记录数（仅第一页需要）
            if is_first_page:
                total_records = data.get('data', {}).get('total', 0)
                print(f"📊 总记录数: {total_records}")
                
                if total_records == 0:
                    print("⚠️ 没有数据可处理")
                    return
            
            # 解析并保存数据
            saved_count = self.parse_and_save_data(data, is_first_page)
            processed_records += saved_count
            
            # 检查是否还有更多数据
            hits = data.get('data', {}).get('hits', [])
            if len(hits) < limit or processed_records >= total_records:
                print("✅ 所有数据处理完成")
                break
            
            # 准备下一页
            offset += limit
            is_first_page = False
            
            # 添加延迟，避免请求过于频繁
            time.sleep(1)
        
        print(f"🎉 处理完成！总共处理了 {processed_records} 条记录")
        print(f"📁 数据已保存到: {self.output_file}")

def main():
    """主函数"""
    try:
        fetcher = TokenHoldersFetcher()
        fetcher.fetch_all_data()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
