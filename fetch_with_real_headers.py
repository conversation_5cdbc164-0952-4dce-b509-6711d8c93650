#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实curl请求的Token Holders数据获取脚本
"""

import requests
import time
import json
import hashlib
import hmac
import base64

def generate_signature(timestamp, token):
    """生成签名 - 这是一个简化版本，实际签名算法可能更复杂"""
    # 这里需要根据实际的签名算法来实现
    # 暂时返回一个占位符
    return "placeholder_signature"

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 从curl命令中提取的关键信息
    api_key = "LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjM3NzAxNzY0OTg="
    dev_id = "5ad6aeac-3ed2-458d-9847-0b0ad35d52e5"
    verify_token = "382dd3b8-e359-44de-92df-ec11163c27cb"
    site_info = "9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye"
    id_group = "2930162120255470001-c-40"
    
    # 基于真实请求的完整头信息
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': dev_id,
        'priority': 'u=1, i',
        'referer': 'https://www.oklink.com/zh-hans/x-layer/token/0x4cf55a735f45271548faed60340ef4658ccb167c?tab=holders',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': api_key,
        'x-cdn': 'https://static.oklink.com',
        'x-id-group': id_group,
        'x-locale': 'zh_CN',
        'x-simulated-trading': 'undefined',
        'x-site-info': site_info,
        'x-utc': '8',
        'x-zkdex-env': '0',
        'ok-verify-token': verify_token,
    }
    
    # 重要的cookies（简化版本，只保留关键的）
    cookies = {
        'devId': dev_id,
        'locale': 'zh_CN',
        'fingerprint_id': dev_id,
        'traceId': '2930162120255470001',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    offset = 0
    limit = 100  # 使用100而不是50，获取更多数据
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取数据...")
    print(f"🔑 使用API密钥: {api_key[:20]}...")
    
    while True:
        # 生成当前时间戳
        current_timestamp = int(time.time() * 1000)
        
        # 添加时间戳相关的头信息
        session.headers.update({
            'ok-timestamp': str(current_timestamp),
            'ok-verify-sign': 'RDaFOUASYs7b1GIIXZ1I/c3vcpWUFiGqXeeIdWKfFOI=',  # 使用原始签名
        })
        
        # 构建请求参数
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页... (offset: {offset}, timestamp: {current_timestamp})")
            
            # 发送请求
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            print(f"📊 API响应: code={data.get('code')}, msg='{data.get('msg', 'N/A')}'")
            
            # 检查响应状态
            if data.get('code') != 0:
                error_msg = data.get('msg', '未知错误')
                print(f"❌ API错误: {error_msg}")
                print(f"详细错误: {data.get('detailMsg', 'N/A')}")
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📈 本页: {len(hits)} 条, 总计: {total_count} 条")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 写入文件
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('tokenContractAddress', '')
                    value_change = item.get('valueChange24h', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已保存 {len(hits)} 条记录，累计 {total_processed}/{total_count} 条")
            
            # 检查是否完成
            if len(hits) < limit or total_processed >= total_count:
                print("📄 所有数据获取完成")
                break
            
            # 准备下一页
            offset += limit
            is_first_write = False
            
            # 延迟2秒
            print("⏳ 等待2秒...")
            time.sleep(2)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            break
    
    print(f"\n🎉 完成！总共处理了 {total_processed} 条记录")
    print(f"📁 数据已保存到: {output_file}")
    
    # 显示文件内容预览
    if total_processed > 0:
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:5]  # 显示前5行
                print(f"\n📄 文件内容预览 (前5行):")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}. {line.strip()}")
                if len(lines) == 5 and total_processed > 5:
                    print(f"  ... 还有 {total_processed - 5} 行")
        except Exception as e:
            print(f"⚠️ 无法预览文件: {e}")

if __name__ == "__main__":
    fetch_token_holders()
