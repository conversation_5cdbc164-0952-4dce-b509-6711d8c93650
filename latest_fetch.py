#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用最新认证信息和动态签名的Token Holders数据获取脚本
"""

import requests
import time
import hashlib
import hmac
import base64
import json

def generate_signature(timestamp, api_key, token):
    """
    尝试生成签名 - 这是一个推测的算法
    实际的签名算法可能更复杂
    """
    # 常见的签名算法模式
    patterns = [
        f"{timestamp}{api_key}{token}",
        f"{api_key}{timestamp}{token}",
        f"{token}{timestamp}{api_key}",
        f"{timestamp}{token}",
        f"{api_key}{timestamp}",
    ]
    
    for pattern in patterns:
        # 尝试不同的哈希算法
        for hash_func in [hashlib.sha256, hashlib.sha1, hashlib.md5]:
            try:
                # HMAC签名
                signature = hmac.new(
                    api_key.encode('utf-8'),
                    pattern.encode('utf-8'),
                    hash_func
                ).digest()
                yield base64.b64encode(signature).decode('utf-8')
                
                # 直接哈希
                signature = hash_func(pattern.encode('utf-8')).digest()
                yield base64.b64encode(signature).decode('utf-8')
            except:
                continue

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 最新的认证信息
    api_key = "LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjQ4MTIzODE2NDM="
    verify_token = "25ab133e-e6a8-4516-82cf-d68239e1380a"
    dev_id = "5ad6aeac-3ed2-458d-9847-0b0ad35d52e5"
    
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': dev_id,
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': api_key,
        'x-cdn': 'https://static.oklink.com',
        'x-id-group': '2920162119777970001-c-28',  # 更新的ID组
        'x-locale': 'zh_CN',
        'x-simulated-trading': 'undefined',
        'x-site-info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        'x-utc': '8',
        'x-zkdex-env': '0',
        'ok-verify-token': verify_token,
        'Referer': 'https://www.oklink.com/zh-hans/x-layer/token/0x4cf55a735f45271548faed60340ef4658ccb167c?tab=holders'
    }
    
    # 更新的cookies
    cookies = {
        'devId': dev_id,
        'locale': 'zh_CN',
        'fingerprint_id': dev_id,
        'traceId': '2930162120255470001',
        'ok-ses-id': '6W/4YsgDpbRLLt0qRQc4XEld3kSK5t6nBS485Hy3n2jaH8ouRF/Q01q6t4/cMoApQmjB+oTXMquRwCixyGn+4CsT4vlknZidXiCFkO2zzcMaNOQ/5i8N4vdGEHULKKcK',
        'ok_site_info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        '_monitor_extras': '{"deviceId":"EN9QcWoSyhN0F-t31hA-QQ","eventId":144,"sequenceNumber":144}',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取Token持有者数据...")
    print(f"🔑 API密钥: {api_key[:30]}...")
    print(f"🎫 验证令牌: {verify_token}")
    
    # 已知的签名示例，用于分析规律
    known_signatures = [
        ("1756213701273", "X/oHQ+ahOdtd5vgegZcg6+q/HmJbxl4sjgMHVdAeWb0="),
        ("1756213442366", "o/OzKMLx4h9epz5AkMoEfKwKLlmqrQ6JtoJ1Vg5Ta8g="),
    ]
    
    while True:
        # 🕐 动态生成当前时间戳（毫秒）
        current_timestamp = int(time.time() * 1000)
        timestamp_str = str(current_timestamp)
        
        # 🔐 尝试生成签名
        generated_signature = None
        
        # 方法1: 使用最新提供的签名（可能很快过期）
        latest_signature = "X/oHQ+ahOdtd5vgegZcg6+q/HmJbxl4sjgMHVdAeWb0="
        
        # 方法2: 尝试生成签名
        print(f"🔍 尝试为时间戳 {timestamp_str} 生成签名...")
        signature_attempts = list(generate_signature(timestamp_str, api_key, verify_token))
        
        if signature_attempts:
            print(f"📝 生成了 {len(signature_attempts)} 个可能的签名")
            # 使用第一个生成的签名
            generated_signature = signature_attempts[0]
        
        # 优先使用生成的签名，如果没有则使用最新提供的
        use_signature = generated_signature if generated_signature else latest_signature
        
        # 更新请求头
        session.headers.update({
            'ok-timestamp': timestamp_str,
            'ok-verify-sign': use_signature,
        })
        
        # 构建请求参数
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页 (offset: {offset})...")
            print(f"🕐 时间戳: {timestamp_str}")
            print(f"🔐 使用签名: {use_signature[:20]}...")
            
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:300]}")
                
                # 如果是签名错误，尝试其他签名
                if response.status_code in [401, 403] and signature_attempts:
                    print("🔄 尝试其他签名...")
                    for i, alt_signature in enumerate(signature_attempts[1:6], 1):  # 尝试前5个
                        print(f"   尝试签名 {i}: {alt_signature[:20]}...")
                        session.headers['ok-verify-sign'] = alt_signature
                        
                        alt_response = session.get(base_url, params=params, timeout=10)
                        if alt_response.status_code == 200:
                            print(f"✅ 签名 {i} 成功！")
                            response = alt_response
                            use_signature = alt_signature
                            break
                        else:
                            print(f"   签名 {i} 失败: {alt_response.status_code}")
                
                if response.status_code != 200:
                    break
            
            try:
                data = response.json()
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:300]}")
                break
            
            print(f"📊 API响应: code={data.get('code')}, msg='{data.get('msg', 'N/A')}'")
            
            if data.get('code') != 0:
                error_msg = data.get('msg', '未知错误')
                print(f"❌ API错误: {error_msg}")
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📈 获取到 {len(hits)} 条记录，总计 {total_count} 条")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 保存数据到文件 - 使用正确的字段名
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    holder_address = item.get('holderAddress', '')  # 修正字段名
                    value = item.get('value', 0)  # 修正字段名
                    f.write(f"{holder_address},{value}\n")
            
            total_processed += len(hits)
            print(f"✅ 已保存，累计 {total_processed}/{total_count} 条")
            
            # 检查是否完成
            if len(hits) < limit:
                print("📄 已到最后一页")
                break
            
            offset += limit
            is_first_write = False
            
            # 延迟1秒
            print("⏳ 等待1秒...")
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            break
    
    print(f"\n🎉 完成！总共获取 {total_processed} 条记录")
    print(f"📁 已保存到: {output_file}")
    
    # 显示文件内容示例
    if total_processed > 0:
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:3]
                print(f"\n📄 文件内容示例:")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}. {line.strip()}")
        except:
            pass

if __name__ == "__main__":
    fetch_token_holders()
