#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间戳生成的脚本
"""

import time
from datetime import datetime

def test_timestamp():
    """测试时间戳生成"""
    
    print("🕐 时间戳测试:")
    
    for i in range(3):
        # 生成当前时间戳（毫秒）
        current_timestamp = int(time.time() * 1000)
        
        # 转换为可读时间
        readable_time = datetime.fromtimestamp(current_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"  {i+1}. 时间戳: {current_timestamp}")
        print(f"     可读时间: {readable_time}")
        print(f"     与你的curl中的时间戳对比: 1756212659065")
        print()
        
        time.sleep(1)

def test_single_request():
    """测试单个请求"""
    import requests
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    
    # 动态生成时间戳
    current_timestamp = int(time.time() * 1000)
    
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': 'LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjM3NzAxNzY0OTg=',
        'x-locale': 'zh_CN',
        'ok-verify-token': '382dd3b8-e359-44de-92df-ec11163c27cb',
        'ok-verify-sign': 'RDaFOUASYs7b1GIIXZ1I/c3vcpWUFiGqXeeIdWKfFOI=',
        'ok-timestamp': str(current_timestamp),  # 动态时间戳
    }
    
    params = {
        'offset': 0,
        'limit': 5,  # 只测试5条
        'sort': 'value,desc',
        't': current_timestamp  # 动态时间戳
    }
    
    print(f"🧪 测试请求:")
    print(f"   时间戳: {current_timestamp}")
    print(f"   URL参数 t: {params['t']}")
    print(f"   请求头 ok-timestamp: {headers['ok-timestamp']}")
    print()
    
    try:
        response = requests.get(base_url, headers=headers, params=params, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📈 API响应码: {data.get('code')}")
            print(f"📝 API消息: {data.get('msg', 'N/A')}")
            
            if data.get('code') == 0:
                hits = data.get('data', {}).get('hits', [])
                total = data.get('data', {}).get('total', 0)
                print(f"✅ 成功！获取到 {len(hits)} 条记录，总计 {total} 条")
                
                if hits:
                    first_item = hits[0]
                    print(f"📄 第一条数据示例:")
                    print(f"   地址: {first_item.get('tokenContractAddress', 'N/A')}")
                    print(f"   24h变化: {first_item.get('valueChange24h', 'N/A')}")
            else:
                print(f"❌ API错误: {data.get('msg', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_timestamp()
    print("="*50)
    test_single_request()
