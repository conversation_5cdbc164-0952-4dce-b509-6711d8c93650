#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用最新认证信息的Token Holders数据获取脚本
"""

import requests
import time

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 使用你最新提供的认证信息
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': 'LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjQ1NTM0Njc1NDY=',  # 新的API密钥
        'x-cdn': 'https://static.oklink.com',
        'x-id-group': '2930162120255470001-c-82',  # 更新的ID组
        'x-locale': 'zh_CN',
        'x-simulated-trading': 'undefined',
        'x-site-info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        'x-utc': '8',
        'x-zkdex-env': '0',
        'ok-verify-token': 'f8c984ff-9a0b-4661-b80e-cfa21af0717a',  # 新的验证令牌
        'Referer': 'https://www.oklink.com/zh-hans/x-layer/token/0x4cf55a735f45271548faed60340ef4658ccb167c?tab=holders'
    }
    
    # 更新的cookies
    cookies = {
        'devId': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'locale': 'zh_CN',
        'fingerprint_id': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'traceId': '2930162120255470001',
        'ok-ses-id': '6W/4YsgTpbRLLt0qRQc4XEplj8JMHaXYseLAiAuF4/gtdOo7erE6apt06h3C1QgdtFFSAwGMWu6B1r2cOJPEy5Utmnr7wBPNcaQHZOZlSUqW2TESs5tNmliyfqy1QFaP',  # 新的session ID
        'ok_site_info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        '_monitor_extras': '{"deviceId":"EN9QcWoSyhN0F-t31hA-QQ","eventId":144,"sequenceNumber":144}',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取Token持有者数据...")
    print("🔑 使用最新的认证信息...")
    
    while True:
        # 🕐 动态生成当前时间戳（毫秒）
        current_timestamp = int(time.time() * 1000)
        
        # ⚠️ 关键：签名可能需要根据时间戳重新计算
        # 这里我们先尝试使用你提供的签名，但可能需要动态生成
        session.headers.update({
            'ok-timestamp': str(current_timestamp),
            'ok-verify-sign': 'o/OzKMLx4h9epz5AkMoEfKwKLlmqrQ6JtoJ1Vg5Ta8g=',  # 你提供的最新签名
        })
        
        # 构建请求参数
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp  # URL参数中的动态时间戳
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页 (offset: {offset}, timestamp: {current_timestamp})...")
            
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:300]}")
                
                # 如果是401或403，可能是认证信息过期
                if response.status_code in [401, 403]:
                    print("⚠️ 认证失败！可能的原因:")
                    print("   1. API密钥已过期")
                    print("   2. 签名已过期")
                    print("   3. Session已过期")
                    print("   💡 请在浏览器中重新获取最新的请求信息")
                break
            
            try:
                data = response.json()
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:300]}")
                break
            
            print(f"📊 API响应: code={data.get('code')}, msg='{data.get('msg', 'N/A')}'")
            
            if data.get('code') != 0:
                error_msg = data.get('msg', '未知错误')
                print(f"❌ API错误: {error_msg}")
                
                # 根据错误类型给出建议
                if 'SIGN' in error_msg.upper() or 'TOKEN' in error_msg.upper():
                    print("💡 签名或令牌错误，请重新获取认证信息")
                elif 'EXPIRED' in error_msg.upper():
                    print("💡 认证信息已过期，请重新获取")
                elif 'API_KEY' in error_msg.upper():
                    print("💡 API密钥问题，请检查密钥是否正确")
                
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📈 获取到 {len(hits)} 条记录，总计 {total_count} 条")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 保存数据到文件
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('holderAddress', '')
                    value_change = item.get('value', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已保存，累计 {total_processed}/{total_count} 条")
            
            # 检查是否完成
            if len(hits) < limit:
                print("📄 已到最后一页")
                break
            
            offset += limit
            is_first_write = False
            
            # 延迟1秒，避免请求过快
            print("⏳ 等待1秒...")
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            break
    
    print(f"\n🎉 完成！总共获取 {total_processed} 条记录")
    print(f"📁 已保存到: {output_file}")
    
    # 显示文件内容示例
    if total_processed > 0:
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:3]
                print(f"\n📄 文件内容示例:")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}. {line.strip()}")
        except:
            pass
    else:
        print("\n💡 如果没有获取到数据，请:")
        print("   1. 在浏览器中刷新页面")
        print("   2. 重新复制最新的fetch请求")
        print("   3. 更新脚本中的认证信息")

if __name__ == "__main__":
    fetch_token_holders()
