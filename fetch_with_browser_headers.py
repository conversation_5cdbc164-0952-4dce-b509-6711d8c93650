#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用完整浏览器头信息的Token Holders数据获取脚本
"""

import requests
import time
import json

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 完整的浏览器请求头，模拟真实浏览器访问
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Host': 'www.oklink.com',
        'Origin': 'https://www.oklink.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.oklink.com/cn/okc/token/0x4cf55a735f45271548faed60340ef4658ccb167c',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"macOS"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    # 创建session保持连接
    session = requests.Session()
    session.headers.update(headers)
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取数据...")
    print("💡 提示：如果仍然提示API_KEY_NOT_FIND，可能需要：")
    print("   1. 先在浏览器中访问 https://www.oklink.com 获取cookies")
    print("   2. 或者需要注册获取API密钥")
    
    while True:
        # 构建请求参数，使用当前时间戳
        current_timestamp = int(time.time() * 1000)
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页... (timestamp: {current_timestamp})")
            
            # 发送请求
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 响应状态码: {response.status_code}")
            
            # 打印响应内容用于调试
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            print(f"📊 API响应码: {data.get('code')}, 消息: {data.get('msg', 'N/A')}")
            
            # 检查响应状态
            if data.get('code') != 0:
                print(f"❌ API错误: {data.get('msg', '未知错误')}")
                print(f"详细错误: {data.get('detailMsg', 'N/A')}")
                
                # 如果是API密钥问题，给出建议
                if 'API_KEY' in str(data.get('msg', '')):
                    print("\n💡 解决建议:")
                    print("1. 访问 https://www.oklink.com/account/my-api 注册并获取API密钥")
                    print("2. 或者尝试在浏览器中先访问该页面，然后复制完整的请求头")
                    print("3. 也可以尝试使用浏览器开发者工具复制curl命令")
                
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📈 本页记录数: {len(hits)}, 总记录数: {total_count}")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 写入文件
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('tokenContractAddress', '')
                    value_change = item.get('valueChange24h', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已处理 {len(hits)} 条记录，累计 {total_processed} 条")
            
            # 如果返回的记录数少于limit，说明是最后一页
            if len(hits) < limit:
                print("📄 已到达最后一页")
                break
            
            # 准备下一页
            offset += limit
            is_first_write = False
            
            # 延迟2秒，避免请求过快
            print("⏳ 等待2秒...")
            time.sleep(2)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            break
    
    print(f"\n🎉 完成！总共处理了 {total_processed} 条记录")
    print(f"📁 数据已保存到: {output_file}")
    
    if total_processed == 0:
        print("\n🔧 如果没有获取到数据，请尝试:")
        print("1. 检查网络连接")
        print("2. 在浏览器中访问 https://www.oklink.com 确认网站可访问")
        print("3. 考虑注册API密钥")

if __name__ == "__main__":
    fetch_token_holders()
