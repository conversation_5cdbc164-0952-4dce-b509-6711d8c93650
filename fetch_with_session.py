#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用session和预访问的Token Holders数据获取脚本
"""

import requests
import time
import json
import re

def get_session_with_cookies():
    """先访问主页获取session和可能的cookies"""
    session = requests.Session()
    
    # 设置基础头信息
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
    })
    
    try:
        print("🔄 正在访问主页获取session...")
        # 先访问主页
        response = session.get('https://www.oklink.com/', timeout=30)
        print(f"✅ 主页访问成功，状态码: {response.status_code}")
        
        # 访问代币页面
        token_page_url = 'https://www.oklink.com/cn/okc/token/0x4cf55a735f45271548faed60340ef4658ccb167c'
        print("🔄 正在访问代币页面...")
        response = session.get(token_page_url, timeout=30)
        print(f"✅ 代币页面访问成功，状态码: {response.status_code}")
        
        # 尝试从页面中提取可能的API密钥或token
        if response.text:
            # 查找可能的API配置
            api_key_patterns = [
                r'apiKey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'API_KEY["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'accessKey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ]
            
            for pattern in api_key_patterns:
                matches = re.findall(pattern, response.text, re.IGNORECASE)
                if matches:
                    print(f"🔍 找到可能的API密钥: {matches[0][:10]}...")
                    return session, matches[0]
        
        return session, None
        
    except Exception as e:
        print(f"⚠️ 预访问失败: {e}")
        return session, None

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    # 先获取session
    session, api_key = get_session_with_cookies()
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 更新请求头为API请求
    session.headers.update({
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://www.oklink.com/cn/okc/token/0x4cf55a735f45271548faed60340ef4658ccb167c',
        'X-Requested-With': 'XMLHttpRequest',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
    })
    
    # 如果找到了API密钥，添加到头信息中
    if api_key:
        session.headers.update({
            'Ok-Access-Key': api_key,
            'X-API-Key': api_key,
            'Authorization': f'Bearer {api_key}',
        })
        print(f"🔑 使用找到的API密钥: {api_key[:10]}...")
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取数据...")
    
    while True:
        # 构建请求参数
        current_timestamp = int(time.time() * 1000)
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页... (offset: {offset})")
            
            # 发送请求
            response = session.get(base_url, params=params, timeout=30)
            
            print(f"🔍 响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
                break
            
            print(f"📊 API响应: code={data.get('code')}, msg='{data.get('msg', 'N/A')}'")
            
            # 检查响应状态
            if data.get('code') != 0:
                error_msg = data.get('msg', '未知错误')
                print(f"❌ API错误: {error_msg}")
                
                # 如果是API密钥问题，提供详细建议
                if 'API_KEY' in error_msg:
                    print("\n💡 API密钥问题解决方案:")
                    print("1. 注册OKLink账户: https://www.oklink.com/account/register")
                    print("2. 获取API密钥: https://www.oklink.com/account/my-api")
                    print("3. 将API密钥添加到请求头中")
                    print("4. 或者使用浏览器开发者工具复制完整请求")
                
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📈 本页: {len(hits)} 条, 总计: {total_count} 条")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 写入文件
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('tokenContractAddress', '')
                    value_change = item.get('valueChange24h', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已保存 {len(hits)} 条记录，累计 {total_processed}/{total_count} 条")
            
            # 检查是否完成
            if len(hits) < limit or total_processed >= total_count:
                print("📄 所有数据获取完成")
                break
            
            # 准备下一页
            offset += limit
            is_first_write = False
            
            # 延迟
            print("⏳ 等待2秒...")
            time.sleep(2)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            break
    
    print(f"\n🎉 完成！总共处理了 {total_processed} 条记录")
    print(f"📁 数据已保存到: {output_file}")

if __name__ == "__main__":
    fetch_token_holders()
