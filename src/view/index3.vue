<template>
  <!-- 模板部分保持不变 -->
  <div class="excel-viewer">
    <div class="controls">
      <!-- 题库选择下拉框 -->
      <div class="category-select-wrapper">
        <el-select
          v-model="selectedGameCategory"
          placeholder="请先选择题库"
          clearable
          style="width: 200px"
          @change="handleCategoryChange"
        >
          <el-option
            v-for="category in gameCategoryOptions"
            :key="category.categoryName"
            :label="category.categoryName"
            :value="category.categoryName"
          />
        </el-select>
      </div>

      <div class="file-upload-wrapper">
        <input
          type="file"
          id="fileInput"
          @change="handleFile"
          accept=".xlsx,.xls"
          class="file-input-hidden"
          :disabled="!selectedGameCategory"
        />
        <label
          for="fileInput"
          class="file-upload-btn"
          :class="{ disabled: !selectedGameCategory }"
        >
          <svg
            class="upload-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7,10 12,15 17,10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          {{
            file
              ? file.name
              : selectedGameCategory
              ? "选择Excel文件"
              : "请先选择题库"
          }}
        </label>
      </div>

      <button
        class="parse-btn"
        @click="parseExcel"
        :disabled="!file || loading || !selectedGameCategory"
        :class="{ loading: loading }"
      >
        <svg v-if="loading" class="btn-spinner" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          ></circle>
          <path
            d="M12 2 A10 10 0 0 1 22 12"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
          ></path>
        </svg>
        <svg
          v-else
          class="parse-icon"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
        >
          <path
            d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          ></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        {{ loading ? loadingText : "上传" }}
      </button>
    </div>

    <!-- Loading 指示器 -->
    <div
      v-if="loading"
      class="loading-indicator"
      style="flex-direction: column"
    >
      <div class="loading-spinner"></div>
      <span>{{ loadingText }}</span>
      <el-progress
        style="width: 100%"
        :text-inside="true"
        :stroke-width="26"
        :percentage="progressPercent"
      />
    </div>

    <div v-if="error" class="error">{{ error }}</div>
    <div v-if="successText" class="successText">{{ successText }}</div>
    <!-- 验证错误表格 -->
    <div v-if="validationErrors.length > 0" class="validation-errors">
      <h3 style="color: red">数据验证失败，以下数据不合规：</h3>
      <el-table
        :data="validationErrors"
        style="width: 100%; margin-bottom: 20px"
        border
        stripe
        height="300"
      >
        <el-table-column
          prop="rowIndex"
          label="行号"
          width="80"
          align="center"
        />
        <el-table-column
          prop="sheetName"
          label="工作表"
          width="120"
          align="center"
        />
        <el-table-column
          prop="content"
          label="题目问题"
          width="300"
          align="center"
        />
        <el-table-column prop="errors" label="错误信息" align="center">
          <template #default="scope">
            <div style="color: red">
              <div v-for="errorMsg in scope.row.errors" :key="errorMsg">
                {{ errorMsg }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 正常数据表格 -->
    <el-table
      v-if="parsedData.length > 0"
      ref="categoryTable"
      :data="parsedData"
      :resizable="false"
      style="width: 100%"
      border
      height="300"
    >
      <el-table-column prop="content" label="题目问题" align="center" />
      <el-table-column prop="image" label="配图" width="120" align="center">
        <template #default="scope">
          <div v-if="scope.row.imageUrl">
            <img
              :src="scope.row.imageUrl"
              style="max-width: 100px; max-height: 80px"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="difficulty"
        label="题目难度"
        width="90"
        align="center"
      />
      <el-table-column
        prop="questionType"
        label="题目类型"
        width="90"
        align="center"
      />
      <el-table-column
        prop="correctAnswer"
        label="正确答案"
        width="200"
        align="center"
      />
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import ExcelJS from "exceljs";
import JSZip from "jszip";
// 移除xml2js，使用原生DOMParser
import {
  stsTokenApi,
  batchCreateQuestions,
  getGameCategories,
} from "@/api/answer.js";
import { ElMessage } from "element-plus";

const file = ref(null);
const loading = ref(false);
const loadingText = ref("解析中..."); // 动态loading文本
const error = ref(null);
const successText = ref(null);
const sheets = ref([]);
const activeSheet = ref("");
const parsedData = ref([]); // 存储转换后的数组格式数据
const validationErrors = ref([]); // 存储验证错误的数据
// 进度百分比（可用于进度条组件）
let progressPercent = ref(0); // 默认进度为60%
// 题库选择相关
const selectedGameCategory = ref("");
const gameCategoryOptions = ref([]);

const activeSheetData = computed(() => {
  return sheets.value.find((sheet) => sheet.name === activeSheet.value);
});

function isImageCell(cell) {
  return cell?.type === "image" || cell?.formula?.includes("DISPIMG");
}

// 获取题库选项
const getGameCategoryOptions = async () => {
  try {
    const response = await getGameCategories();
    if (response.code === 200) {
      gameCategoryOptions.value = response.data || [];
    }
  } catch (error) {
    ElMessage.error("获取题库选项失败");
  }
};

// 题库选择改变事件
const handleCategoryChange = (value) => {
  // 清空文件选择和数据
  clearFileInput();
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = [];
  error.value = null;
  successText.value = null;
};

// 重置所有数据，包括错误表格、正确表格等
const reset = () => {
  clearFileInput();
  selectedGameCategory.value = "";
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = [];
  error.value = null;
  successText.value = null;
  getGameCategoryOptions();
};
defineExpose({
  reset,
});
let fileName = ref("");
const handleFile = (e) => {
  if (!selectedGameCategory.value) {
    ElMessage.warning("请先选择题库");
    return;
  }

  file.value = e.target.files[0];
  fileName.value = e.target.files[0];
  error.value = null;
  successText.value = null;
  // 每次选择新文件时清空之前的数据
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = []; // 清空验证错误
};

// 清空文件输入的函数
const clearFileInput = () => {
  file.value = null;
  // 同时清空HTML input元素的值
  const fileInput = document.getElementById("fileInput");
  if (fileInput) {
    fileInput.value = "";
  }
};
let totalCountNum = ref(0);
let successCountNum = ref(0);
let failCountNum = ref(0);
let duplicateCountNum = ref(0);
let imageUploadFailCountNum = ref(0); // 图片上传失败的题目数量
let parsedTotalCount = ref(0); // 解析的总题目数量
// ✅ 分批上传题目数据到服务器（每批100条，带进度条）
const uploadQuestionsToServer = async (questions) => {
  totalCountNum.value = 0;
  successCountNum.value = 0;
  failCountNum.value = 0;
  duplicateCountNum.value = 0;
  imageUploadFailCountNum.value = 0;
  try {
    // 过滤掉图片解析失败的题目
    questions = questions.filter((question) => {
      return question.imageUrl !== "图片解析失败";
    });

    const batchSize = 100;
    const total = questions.length;
    let uploaded = 0;
    let batchCount = 0;
    let allSuccess = true;
    let lastResponse = null;

    for (let i = 0; i < total; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);
      batchCount++;
      // 上传进度在60%基础上递增，最多到100%
      progressPercent.value = 60 + Math.round((uploaded / total) * 40);
      // 如果有进度条组件，可以设置 progress.value = progressPercent

      // 执行上传
      let response;
      try {
        response = await batchCreateQuestions(batch);
        lastResponse = response;
        if (response && response.code === 200) {
          // 这里需要累加每一批的数量
          totalCountNum.value += response.data.totalCount;
          successCountNum.value += response.data.successCount;
          failCountNum.value += response.data.failCount;
          duplicateCountNum.value += response.data.duplicateCount;
        } else {
          allSuccess = false;
          // 失败不中断，继续执行下一次
        }
      } catch (err) {
        allSuccess = false;
        // 失败不中断，继续执行下一次
      }

      uploaded += batch.length;
      // 更新进度
      progressPercent.value = Math.round((uploaded / total) * 100);
      // 如果有进度条组件，可以设置 progress.value = progressPercent
    }

    clearFileInput();

    if (allSuccess && lastResponse && lastResponse.code === 200) {
      successText.value = `解析${parsedTotalCount.value}道题、上传${totalCountNum.value}道题目、成功${successCountNum.value}道题、失败${failCountNum.value}道题、重复${duplicateCountNum.value}道题`;
    } else {
      throw new Error(lastResponse?.message || "部分或全部上传失败");
    }
  } catch (err) {
    clearFileInput();
    throw new Error(
      `解析${parsedTotalCount.value}道题、上传题目失败: ${err.message}`
    );
  }
};

// ✅ 数据预验证函数 - 在上传OSS之前验证数据
const preValidateExcelData = async (workbook) => {
  const errors = [];
  const parsedSheets = [];

  // 处理每个工作表进行预验证
  workbook.eachSheet((worksheet) => {
    const sheetData = {
      name: worksheet.name,
      headers: [],
      jsonData: [],
    };

    // 提取表头
    const headerRow = worksheet.getRow(1);
    if (headerRow?.values) {
      sheetData.headers = headerRow.values.slice(1);
    }

    const jsonRows = [];

    // 处理数据行进行验证
    worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
      if (rowIndex === 1) return; // 跳过标题行

      const jsonRowData = {};
      let hasContent = false;

      // 检查这一行是否为空行
      row.eachCell({ includeEmpty: true }, (cell) => {
        if (cell.value || cell.formula || cell.text) {
          hasContent = true;
        }
      });

      // 如果整行都是空的，跳过这一行
      if (!hasContent) {
        return;
      }

      // 提取行数据
      row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
        const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

        // 对于图片单元格，暂时标记为图片类型
        if (cell.formula?.includes("DISPIMG")) {
          jsonRowData[headerName] = { type: "image", placeholder: true };
        } else {
          // 🔧 智能处理单元格内容：优先使用cell.value，如果不是字符串则处理富文本
          let cellValue = cell.value;

          // 如果cell.value不是字符串类型，尝试提取富文本内容
          if (typeof cellValue !== "string") {
            cellValue = extractTextFromRichText(cellValue);
          }

          // 如果还是没有值，尝试cell.text
          if (!cellValue && cell.text) {
            if (typeof cell.text === "string") {
              cellValue = cell.text;
            } else {
              cellValue = extractTextFromRichText(cell.text);
            }
          }

          jsonRowData[headerName] = cellValue || null;
        }
      });

      jsonRows.push(jsonRowData);
    });

    sheetData.jsonData = jsonRows;
    parsedSheets.push(sheetData);
  });

  // 进行数据验证
  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name;

    // 验证题目类型是否合法
    const validQuestionTypes = ["判断题", "选择题", "问答题"];
    if (!validQuestionTypes.includes(questionType)) {
      throw new Error(
        `工作表名称错误: "${questionType}"\n\n 支持的题目类型:\n• 判断题\n• 选择题\n• 问答题\n\n 请将工作表重命名为以上类型之一`
      );
    }

    sheet.jsonData.forEach((row, rowIndex) => {
      const questionData = {
        gameCategory: selectedGameCategory.value, // 使用页面选择的题库
        content: row["问题"] || "",
        difficulty: row["题目难度"] || "",
        questionType: questionType,
        correctAnswer:
          typeof row["正确答案"] === "string"
            ? row["正确答案"].replace(/\n/g, "")
            : row["正确答案"] || "",
        answerAnalysis: row["答案解析"] || "",
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
      }
    });
  });

  return {
    hasErrors: errors.length > 0,
    errors: errors,
    parsedSheets: parsedSheets,
  };
};

const parseExcel = async () => {
  if (!selectedGameCategory.value) {
    error.value = "请先选择题库";
    return;
  }
  if (!file.value) {
    error.value = "请先选择文件";
    return;
  }
  progressPercent.value = 1;
  loading.value = true;
  loadingText.value = "正在上传文件...";
  error.value = null;
  successText.value = null;
  sheets.value = [];
  validationErrors.value = [];
  parsedTotalCount.value = 0; // 重置解析题目数量

  try {
    // 真实进度：读取文件和加载workbook时动态递增进度条到10
    progressPercent.value = 0;

    // 1. 读取文件为ArrayBuffer
    let readProgress = 0;
    const readPromise = new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (error) => reject(error);
      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          // 读取文件阶段占0~5
          readProgress = Math.floor((event.loaded / event.total) * 5);
          progressPercent.value = Math.max(progressPercent.value, readProgress);
        }
      };
      reader.readAsArrayBuffer(file.value);
    });

    const arrayBuffer = await readPromise;

    // 2. 加载Excel文件，动态递增到10
    const workbook = new ExcelJS.Workbook();
    let loadProgress = 5;
    // ExcelJS没有原生进度回调，这里用定时器模拟加载进度
    const loadPromise = workbook.xlsx.load(arrayBuffer);
    while (true) {
      // 模拟加载进度，逐步递增到10
      await new Promise((resolve) => setTimeout(resolve, 40));
      if (progressPercent.value < 9) {
        progressPercent.value += 1;
      }
      // 检查是否加载完成
      if (loadPromise && loadPromise.then) {
        const isLoaded = await Promise.race([
          loadPromise.then(() => true),
          new Promise((resolve) => setTimeout(() => resolve(false), 0)),
        ]);
        if (isLoaded) break;
      } else {
        break;
      }
    }
    await loadPromise;
    progressPercent.value = 10;
    // 1. 数据预验证
    const preValidationResult = await preValidateExcelData(workbook);
    if (preValidationResult.hasErrors) {
      validationErrors.value = preValidationResult.errors;
      error.value = `数据验证失败（文件名: ${
        fileName.value?.name || "未知文件"
      }），共发现 ${preValidationResult.errors.length} 条不合规数据`;
      clearFileInput();
      return;
    }
    // 2. 基于解析Excel内部XML结构的图片处理方案
    // 2.1 尝试解析Excel内部XML结构获取DISPIMG映射
    const dispimgMapping = await parseExcelInternalStructure(
      arrayBuffer,
      workbook
    );
    // 2.2 收集所有图片信息
    const allImages = [];

    for (let i = 0; i < workbook.media.length; i++) {
      const media = workbook.media[i];
      const buffer = media.buffer;
      const hash = generateImageHash(buffer);

      allImages.push({
        id: i,
        media,
        hash,
        buffer,
        type: media.type,
      });
    }
    // 2.2 建立工作表图片位置映射 - 基于GitHub issue #551的正确方法
    const sheetImagePositionMap = new Map();

    // 使用for循环而不是eachSheet，确保能看到调试信息
    for (let i = 0; i < workbook.worksheets.length; i++) {
      const worksheet = workbook.worksheets[i];
      console.log(`🔍 处理工作表: ${worksheet.name}`);

      const positionMap = new Map();
      const images = worksheet.getImages();
      console.log(
        `📊 工作表 ${worksheet.name} 中通过getImages()找到 ${images.length} 个浮动图片`
      );

      images.forEach((img, imgIndex) => {
        // 使用 nativeRow 和 nativeCol 获取准确的位置（基于GitHub讨论#2503的建议）
        const row = img.range.tl.nativeRow + 1; // nativeRow 是0基础的，需要+1
        const col = img.range.tl.nativeCol + 1; // nativeCol 是0基础的，需要+1
        const key = `${row}-${col}`;

        // 根据GitHub讨论#2503，使用 workbook.model.media 来查找正确的媒体资源
        // 关键：imageId 对应 media.index
        const mediaResource = workbook.model.media.find(
          (m) => m.index === img.imageId
        );

        if (mediaResource) {
          // 直接存储imageId，而不是allImages的索引
          // 这样可以确保重复图片的正确映射
          positionMap.set(key, img.imageId);
          console.log(
            `✅ 浮动图片映射成功: 位置${key} -> 媒体索引${img.imageId}`
          );
        } else {
          console.warn(
            `⚠️ 浮动图片未找到对应媒体资源: imageId=${img.imageId} 在位置 ${key}`
          );
        }
      });

      // 🔧 修复：同时处理浮动图片和插入式图片，不再用positionMap.size === 0作为条件
      // 无论是否通过getImages()找到图片，都要检查DISPIMG公式中的插入式图片
      let dispimgCount = 0;
      let dispimgMappedCount = 0;
      let dispimgSkippedCount = 0;

      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell, colNumber) => {
          if (cell.formula && cell.formula.includes("DISPIMG")) {
            dispimgCount++;
            const key = `${rowNumber}-${colNumber}`;

            // 🔧 关键修复：检查这个位置是否已经被浮动图片占用
            // 如果已经有图片映射，优先保留浮动图片的映射，避免冲突
            if (!positionMap.has(key)) {
              // 从DISPIMG公式中提取图片ID
              const dispimgMatch = cell.formula.match(/DISPIMG\("([^"]+)"/);
              if (dispimgMatch) {
                const imageIdFromFormula = dispimgMatch[1];

                // 🔧 新方案：直接通过XML映射建立位置关系，绕过文件名匹配
                const mediaResourceFromXML = dispimgMapping[imageIdFromFormula];

                if (mediaResourceFromXML) {
                  // 直接使用XML映射中的媒体资源索引
                  // 这样可以避免文件名匹配的问题
                  const mediaIndex = mediaResourceFromXML.index;

                  // 验证这个索引在workbook.model.media中存在
                  const workbookMedia = workbook.model.media.find(
                    (m) => m.index === mediaIndex
                  );
                  if (workbookMedia) {
                    positionMap.set(key, mediaIndex);
                    dispimgMappedCount++;
                    console.log(
                      `✅ 插入式图片映射成功: 位置${key} -> 媒体索引${mediaIndex}`
                    );
                  }
                } else {
                  console.warn(
                    `⚠️ 未找到DISPIMG图片的XML映射: ${imageIdFromFormula} 在位置 ${key}`
                  );
                }
              }
            } else {
              dispimgSkippedCount++;
              console.log(`ℹ️ 位置${key}已被浮动图片占用，跳过DISPIMG处理`);
            }
          }
        });
      });

      console.log(
        `📊 工作表 ${worksheet.name} DISPIMG统计: 总数${dispimgCount}, 成功映射${dispimgMappedCount}, 跳过${dispimgSkippedCount}`
      );

      sheetImagePositionMap.set(worksheet.name, positionMap);
      console.log(
        `📋 工作表 ${worksheet.name} 最终图片位置映射数量: ${positionMap.size}`
      );
    }

    // 🔧 添加总体图片映射统计
    let totalMappedImages = 0;
    sheetImagePositionMap.forEach((positionMap, sheetName) => {
      totalMappedImages += positionMap.size;
    });
    console.log(
      `🎯 所有工作表图片位置映射总数: ${totalMappedImages}, 媒体资源总数: ${allImages.length}`
    );

    // 2.3 重新设计图片上传和映射逻辑 - 修复重复图片位置问题
    const imageIdToUrlMap = new Map();
    const uploadedHashes = new Map();

    // 统计重复图片
    const hashCounts = new Map();
    for (const image of allImages) {
      hashCounts.set(image.hash, (hashCounts.get(image.hash) || 0) + 1);
    }
    // 🔧 新的并发控制图片上传逻辑
    const uploadedCount = { value: 0 };
    const totalImages = allImages.length;
    const startPercent = 10;
    const endPercent = 60;
    const percentStep =
      totalImages > 0 ? (endPercent - startPercent) / totalImages : 0;

    // 并发上传图片，最大并发数为6
    await uploadImagesWithConcurrency(
      allImages,
      workbook,
      uploadedHashes,
      imageIdToUrlMap,
      uploadedCount,
      percentStep,
      startPercent,
      6 // 最大并发数
    );

    // 3. 处理工作表数据
    const parsedSheets = [];

    workbook.eachSheet((worksheet) => {
      const sheetData = {
        name: worksheet.name,
        headers: [],
        jsonData: [],
      };

      // 提取表头
      const headerRow = worksheet.getRow(1);
      if (headerRow?.values) {
        sheetData.headers = headerRow.values.slice(1);
      }

      // 获取当前工作表的图片位置映射
      const positionMap =
        sheetImagePositionMap.get(worksheet.name) || new Map();

      // 处理数据行
      const jsonRows = [];
      worksheet.eachRow((row, rowIndex) => {
        if (rowIndex === 1) return; // 跳过标题行

        const jsonRowData = {};

        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

          // 检查是否为图片相关的单元格
          const positionKey = `${rowIndex}-${colIndex}`;
          let imageUrl = null;
          let hasImageContent = false; // 标记是否检测到图片内容

          // 0. 只在配图列检查HTTP/HTTPS链接
          if (headerName === "配图") {
            const cellValue = cell.value;

            // 检查字符串类型的链接
            if (
              typeof cellValue === "string" &&
              (cellValue.startsWith("http://") ||
                cellValue.startsWith("https://"))
            ) {
              imageUrl = cellValue;
              hasImageContent = true; // 发现图片内容
            }
            // 检查对象类型的链接（Excel超链接）
            else if (typeof cellValue === "object" && cellValue !== null) {
              // 检查是否为Excel超链接对象
              if (cellValue.hyperlink) {
                imageUrl = cellValue.hyperlink;
                hasImageContent = true; // 发现图片内容
              }
              // 检查是否有text属性包含链接
              else if (
                cellValue.text &&
                typeof cellValue.text === "string" &&
                (cellValue.text.startsWith("http://") ||
                  cellValue.text.startsWith("https://"))
              ) {
                imageUrl = cellValue.text;
                hasImageContent = true; // 发现图片内容
              }
              // 检查其他可能的属性
              else {
                // 尝试查找包含http的属性
                for (const [key, value] of Object.entries(cellValue)) {
                  if (
                    typeof value === "string" &&
                    (value.startsWith("http://") ||
                      value.startsWith("https://"))
                  ) {
                    imageUrl = value;
                    hasImageContent = true; // 发现图片内容
                    break;
                  }
                }
              }
            }
            // 3. 如果没有找到HTTP链接，检查是否包含DISPIMG公式
            if (!imageUrl && cell.formula?.includes("DISPIMG")) {
              hasImageContent = true; // 发现DISPIMG图片内容
              // 1. 尝试精确位置匹配 - 基于GitHub讨论#2503的修复
              const imageId = positionMap.get(positionKey);

              if (imageId !== undefined && imageIdToUrlMap.has(imageId)) {
                imageUrl = imageIdToUrlMap.get(imageId);
              }
              // 1.5 如果精确匹配失败，使用占位符，避免图片错位
              else if (imageId !== undefined) {
                // 尝试通过哈希查找重复图片的URL
                const targetImage = allImages.find((img) => {
                  const mediaResource = workbook.model.media.find(
                    (m) => m === img.media // 修复：使用正确的媒体对象比较
                  );
                  return mediaResource && mediaResource.index === imageId;
                });

                if (targetImage) {
                  // 查找具有相同哈希的其他图片的URL
                  for (const [
                    mappedImageId,
                    mappedUrl,
                  ] of imageIdToUrlMap.entries()) {
                    const mappedImage = allImages.find((img) => {
                      const mediaResource = workbook.model.media.find(
                        (m) => m === img.media // 修复：使用正确的媒体对象比较
                      );
                      return (
                        mediaResource && mediaResource.index === mappedImageId
                      );
                    });

                    if (
                      mappedImage &&
                      mappedImage.hash === targetImage.hash &&
                      mappedUrl
                    ) {
                      imageUrl = mappedUrl;
                      break;
                    }
                  }
                }

                // 如果还是没找到，使用占位符
                if (!imageUrl) {
                  imageUrl = "图片解析失败";
                }
              }
              // 2. 如果没有找到imageId，使用占位符，避免错位
              else {
                // 使用占位符，避免图片错位
                imageUrl = "图片解析失败";
              }
            }

            // 设置配图数据
            if (headerName === "配图") {
              // 🔧 修复：区分"没有图片"和"有图片但上传失败"两种情况
              if (
                imageUrl &&
                typeof imageUrl === "string" &&
                (imageUrl.startsWith("http://") ||
                  imageUrl.startsWith("https://"))
              ) {
                // 图片上传成功
                jsonRowData[headerName] = { type: "image", data: imageUrl };
              } else if (
                hasImageContent &&
                (imageUrl === null || imageUrl === "图片解析失败")
              ) {
                // 🔧 关键修复：只有当检测到图片内容但上传失败时，才标记为失败
                jsonRowData[headerName] = {
                  type: "image",
                  data: null,
                  uploadFailed: true,
                };
              } else {
                // 题目本来就没有图片内容
                jsonRowData[headerName] = null;
              }
            }
          } else {
            // 对于配图列，如果没有检测到链接或DISPIMG，设置为null
            if (headerName === "配图") {
              jsonRowData[headerName] = null;
            } else {
              // 🔧 智能处理单元格内容：优先使用cell.value，如果不是字符串则处理富文本
              let cellValue = cell.value;
              let cellFormat = null;

              // 检查单元格是否有背景颜色或其他格式
              if (cell.fill && cell.fill.fgColor) {
                cellFormat = {
                  backgroundColor: convertArgbToHex(
                    cell.fill.fgColor.argb || cell.fill.fgColor.rgb
                  ),
                };
              }

              // 检查是否是选项列，选项列需要保留富文本格式
              const isOptionsColumn = headerName === "选项";

              // 如果cell.value不是字符串类型，尝试提取富文本内容
              if (typeof cellValue !== "string") {
                cellValue = extractTextFromRichText(cellValue, isOptionsColumn);
              }

              // 如果还是没有值，尝试cell.text
              if (!cellValue && cell.text) {
                if (typeof cell.text === "string") {
                  cellValue = cell.text;
                } else {
                  cellValue = extractTextFromRichText(
                    cell.text,
                    isOptionsColumn
                  );
                }
              }

              // 如果有格式信息或富文本内容，创建富文本对象
              if (cellFormat || (cellValue && cellValue.includes("<span"))) {
                jsonRowData[headerName] = {
                  type: "richText",
                  content: cellValue || "",
                  format: cellFormat,
                };
              } else {
                jsonRowData[headerName] = cellValue || null;
              }
            }
          }
        });

        jsonRows.push(jsonRowData);
      });

      sheetData.jsonData = jsonRows;
      parsedSheets.push(sheetData);
    });

    // 4. 生成最终数据
    const finalJsonData = generateBackendJson(parsedSheets);
    parsedData.value = finalJsonData;

    // 5. 上传数据到服务器
    loadingText.value = "正在上传题目数据...";

    await uploadQuestionsToServer(finalJsonData);

    ElMessage.success(`处理成功！共处理 ${finalJsonData.length} 条数据`);
  } catch (err) {
    console.error("解析失败:", err);
    error.value =
      `（文件名: ${fileName.value?.name || "未知文件"}）` + err.message;
    ElMessage.error(`解析失败: ${err.message}`);
  } finally {
    loading.value = false;
    loadingText.value = "解析中...";
  }
};
// ✅ 生成后端需要的JSON格式数据
function generateBackendJson(parsedSheets) {
  const finalResult = [];
  const errors = [];
  let skippedQuestionCount = 0; // 跟踪实际跳过的题目数

  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name; // 判断题、选择题、问答题等

    // 验证题目类型是否合法
    const validQuestionTypes = ["判断题", "选择题", "问答题"];
    if (!validQuestionTypes.includes(questionType)) {
      throw new Error(
        `（文件名: ${
          fileName.value?.name || "未知文件"
        }）工作表名称错误: "${questionType}"\n\n✅ 支持的题目类型:\n• 判断题\n• 选择题\n• 问答题\n\n💡 请将工作表重命名为以上类型之一`
      );
    }

    sheet.jsonData.forEach((row, rowIndex) => {
      // 统计解析的题目数量（不做任何过滤）
      parsedTotalCount.value++;

      // 处理富文本内容的函数
      const processRichTextContent = (cellData) => {
        if (
          cellData &&
          typeof cellData === "object" &&
          cellData.type === "richText"
        ) {
          // 直接返回HTML格式的富文本内容
          return cellData.content || "";
        }
        return typeof cellData === "string"
          ? cellData.replace(/\n/g, "")
          : cellData || "";
      };

      const questionData = {
        gameCategory: selectedGameCategory.value, // 使用页面选择的题库
        content: processRichTextContent(row["问题"]),
        imageUrl: null,
        difficulty: processRichTextContent(row["题目难度"]),
        questionType: questionType,
        correctAnswer: processRichTextContent(row["正确答案"]),
        answerAnalysis: processRichTextContent(row["答案解析"]),
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
        return; // 跳过有错误的数据
      }

      // 处理配图
      if (row["配图"] && typeof row["配图"] === "object") {
        const imageObj = row["配图"];

        // 🔧 检查是否是图片上传失败的情况
        if (imageObj.uploadFailed === true) {
          // 有图片但上传失败，跳过这个题目
          console.warn(`跳过图片上传失败的题目: "${questionData.content}"`);
          skippedQuestionCount++; // 增加跳过计数
          return; // 跳过这个题目
        }

        // 处理正常的图片数据
        if (imageObj.data && typeof imageObj.data === "string") {
          if (
            imageObj.data.startsWith("http://") ||
            imageObj.data.startsWith("https://")
          ) {
            questionData.imageUrl = imageObj.data;
          } else if (imageObj.data.startsWith("data:")) {
            // 发现 base64 数据，抛出错误
            throw new Error(
              `检测到不允许的base64图片数据，请确保所有图片都已正确上传到服务器。问题出现在题目: "${questionData.content}"`
            );
          } else {
            // 其他无效的图片数据
            // console.warn(
            //   `无效的图片数据格式: "${imageObj.data}", 题目: "${questionData.content}"`
            // );
            // 不设置 imageUrl，让题目没有配图
          }
        }
      }
      // 如果 row["配图"] 为 null 或不存在，说明题目本来就没有图片，正常处理

      // 处理选择题的选项
      if (questionType === "选择题" && row["选项"]) {
        const optionsData = row["选项"];
        const optionsList = [];

        // console.log("原始选项数据:", optionsData, "类型:", typeof optionsData);

        // 处理不同类型的选项数据
        if (Array.isArray(optionsData)) {
          // 如果选项是数组，直接处理每个元素
          console.log("选项是数组格式，数组内容:", optionsData);
          optionsData.forEach((option, index) => {
            let optionText = "";

            if (
              option &&
              typeof option === "object" &&
              option.type === "richText"
            ) {
              optionText = option.content || "";
              console.log(`选项 ${index + 1} 是富文本:`, optionText);
            } else if (typeof option === "string") {
              optionText = option;
              console.log(`选项 ${index + 1} 是字符串:`, optionText);
            } else {
              optionText = String(option || "");
              console.log(`选项 ${index + 1} 转换为字符串:`, optionText);
            }

            // 清理选项文本
            const cleanOption = optionText
              .replace(/^[A-Z][:：]\s*/, "")
              .replace(/【答案】/g, "")
              .trim();
            if (cleanOption) {
              optionsList.push(cleanOption);
              console.log(`最终选项 ${index + 1}:`, cleanOption);
            }
          });
        } else {
          // 处理字符串或富文本格式的选项
          let optionsText = "";
          let isRichText = false;

          if (
            optionsData &&
            typeof optionsData === "object" &&
            optionsData.type === "richText"
          ) {
            optionsText = optionsData.content || "";
            isRichText = true;
            console.log("选项是富文本格式:", optionsText);
          } else if (typeof optionsData === "string") {
            optionsText = optionsData;
            console.log("选项是字符串格式:", optionsText);
          } else {
            optionsText = String(optionsData || "");
            console.log("选项转换为字符串:", optionsText);
          }

          // 确保 optionsText 是字符串
          if (typeof optionsText !== "string") {
            optionsText = String(optionsText);
          }

          if (isRichText) {
            // 使用专门的富文本选项处理函数
            const richTextOptions = processOptionsRichText(optionsText);
            optionsList.push(...richTextOptions);
          } else {
            // 普通字符串选项，按行分割
            const lines = optionsText.split("\n").filter((line) => {
              const lineStr = String(line || "");
              return lineStr.trim() !== "";
            });

            lines.forEach((line, index) => {
              const lineStr = String(line || "");
              const trimmedLine = lineStr.trim();
              if (trimmedLine) {
                // 去掉A:、B:、C:、D:等前缀（支持中英文冒号）
                const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
                // 去掉【答案】标记
                const finalOption = cleanOption.replace(/【答案】/g, "").trim();
                if (finalOption) {
                  optionsList.push(finalOption);
                  console.log(`普通选项 ${index + 1}:`, finalOption);
                }
              }
            });
          }
        }

        questionData.optionsList = optionsList;
        console.log("最终选项列表:", optionsList);
      }

      finalResult.push(questionData);
    });
  });

  // 注意：这里不再设置验证错误，因为预验证已经处理了
  // 如果执行到这里，说明预验证已经通过，不应该有错误

  // 🔧 更新图片上传失败的题目计数
  // imageUploadFailCountNum.value = skippedQuestionCount;

  return finalResult;
}

// ✅ 验证题目数据
function validateQuestionData(questionData, row, questionType) {
  const errors = [];

  // 获取富文本内容的纯文本用于验证
  const getTextContent = (data) => {
    if (data && typeof data === "object" && data.isRichText) {
      // 从HTML中提取纯文本
      const div = document.createElement("div");
      div.innerHTML = data.content;
      return div.textContent || div.innerText || "";
    }
    return data || "";
  };

  // 验证题目问题
  const contentText = getTextContent(questionData.content);
  if (!contentText || contentText.trim() === "") {
    errors.push("题目问题不能为空");
  }

  // 验证题目难度
  const difficultyText = getTextContent(questionData.difficulty);
  if (!difficultyText || difficultyText.trim() === "") {
    errors.push("题目难度不能为空");
  }

  // 注意：不再验证所属题库，因为会从页面选择的题库统一设置

  // 验证题目类型
  if (
    !questionData.questionType ||
    (typeof questionData.questionType === "string" &&
      questionData.questionType.trim() === "")
  ) {
    errors.push("题目类型不能为空");
  }

  // 验证正确答案
  const correctAnswerText = getTextContent(questionData.correctAnswer);
  if (!correctAnswerText || correctAnswerText.trim() === "") {
    errors.push("正确答案不能为空");
  }

  // 根据题目类型进行特定验证
  if (questionType === "选择题") {
    // 选择题验证选项
    const optionsData = row["选项"];

    // 处理不同类型的选项数据进行验证
    let hasValidOptions = false;

    if (Array.isArray(optionsData)) {
      // 如果是数组，检查是否有有效选项
      hasValidOptions = optionsData.some((option) => {
        let optionText = "";
        if (
          option &&
          typeof option === "object" &&
          option.type === "richText"
        ) {
          optionText = option.content || "";
        } else if (typeof option === "string") {
          optionText = option;
        } else {
          optionText = String(option || "");
        }
        return optionText.trim() !== "";
      });
    } else if (
      optionsData &&
      typeof optionsData === "object" &&
      optionsData.type === "richText"
    ) {
      // 富文本格式
      const optionsText = optionsData.content || "";
      hasValidOptions = optionsText.trim() !== "";
    } else if (typeof optionsData === "string") {
      // 字符串格式
      hasValidOptions = optionsData.trim() !== "";
    } else {
      // 其他格式，转换为字符串检查
      const optionsText = String(optionsData || "");
      hasValidOptions = optionsText.trim() !== "";
    }

    if (!hasValidOptions) {
      errors.push("选择题的选项不能为空");
    } else if (typeof optionsData === "string") {
      // 只有字符串格式才需要按行分割验证
      const lines = optionsData.split("\n").filter((line) => line.trim());
      const validOptions = lines.filter((line) => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
          const finalOption = cleanOption.replace(/【答案】/g, "").trim();
          return finalOption !== "";
        }
        return false;
      });

      if (validOptions.length < 2) {
        errors.push("选择题至少需要2个有效选项");
      }
    }
  } else if (questionType === "问答题") {
    // 问答题特殊验证
    const optionsData = row["选项"];

    // 1. 检查是否存在选项列且有值
    let hasOptions = false;

    if (Array.isArray(optionsData)) {
      hasOptions = optionsData.some((option) => {
        let optionText = "";
        if (
          option &&
          typeof option === "object" &&
          option.type === "richText"
        ) {
          optionText = option.content || "";
        } else if (typeof option === "string") {
          optionText = option;
        } else {
          optionText = String(option || "");
        }
        return optionText.trim() !== "";
      });
    } else if (
      optionsData &&
      typeof optionsData === "object" &&
      optionsData.type === "richText"
    ) {
      const optionsText = optionsData.content || "";
      hasOptions = optionsText.trim() !== "";
    } else if (typeof optionsData === "string") {
      hasOptions = optionsData.trim() !== "";
    } else if (optionsData) {
      const optionsText = String(optionsData);
      hasOptions = optionsText.trim() !== "";
    }

    if (hasOptions) {
      errors.push("问答题不应该有选项内容，请清空选项列");
    }

    // 2. 检查答案不能是"是"或"否"
    const answerText = getTextContent(questionData.correctAnswer);
    if (answerText) {
      const trimmedAnswer = answerText.trim();
      if (trimmedAnswer === "是" || trimmedAnswer === "否") {
        errors.push('问答题的答案不能是"是"或"否"，请提供详细答案');
      }
    }
  } else if (questionType === "判断题") {
    // 判断题特殊验证
    const optionsText = row["选项"];

    // 1. 检查是否存在选项列且有值
    if (optionsText && optionsText.trim() !== "") {
      errors.push("判断题不应该有选项内容，请清空选项列");
    }
    // 判断题严格验证答案
    const answerText = getTextContent(questionData.correctAnswer);
    if (answerText) {
      const trimmedAnswer = answerText.trim();
      if (trimmedAnswer !== "是" && trimmedAnswer !== "否") {
        errors.push(
          `判断题的答案必须是"是"或"否"，当前答案："${trimmedAnswer}"`
        );
      }
    } else {
      errors.push('判断题的答案必须是"是"或"否"');
    }
  }

  return errors;
}

// ✅ 统计图片数量
function countImages(jsonData) {
  let count = 0;
  jsonData.forEach((row) => {
    Object.values(row).forEach((cell) => {
      if (cell && typeof cell === "object" && cell.type === "image") {
        count++;
      }
    });
  });
  return count;
}

// ✅ 导出JSON数据的方法
const exportJsonData = () => {
  if (parsedData.value.length === 0) {
    error.value = "没有数据可导出";
    return;
  }

  // 创建下载链接
  const dataStr = JSON.stringify(parsedData.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `questions_data_${new Date().getTime()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 文件处理辅助函数
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

// 🔧 已移除 arrayBufferToBase64 函数，因为不再使用 base64 回退方案

// ✅ 解析Excel文件内部XML结构，获取DISPIMG图片映射关系
const parseExcelInternalStructure = async (arrayBuffer, workbook) => {
  try {
    // 使用JSZip解压Excel文件
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(arrayBuffer);

    // 检查是否存在cellimages相关文件
    const cellimagesXmlFile = zipContent.file("xl/cellimages.xml");
    const cellimagesRelsFile = zipContent.file("xl/_rels/cellimages.xml.rels");

    if (!cellimagesXmlFile || !cellimagesRelsFile) {
      return null;
    }

    // 读取XML文件内容
    const cellimagesXmlContent = await cellimagesXmlFile.async("text");
    const cellimagesRelsContent = await cellimagesRelsFile.async("text");

    // 使用DOMParser解析XML内容
    const parser = new DOMParser();
    const cellimagesDoc = parser.parseFromString(
      cellimagesXmlContent,
      "text/xml"
    );
    const cellimagesRelsDoc = parser.parseFromString(
      cellimagesRelsContent,
      "text/xml"
    );

    // 建立图片映射关系
    const cellimagesNameRidMap = {};
    const result = {};

    // 处理cellimages.xml中的图片信息
    const cellImageElements = cellimagesDoc.querySelectorAll(
      "etc\\:cellImage, cellImage"
    );

    cellImageElements.forEach((cellImage) => {
      try {
        // 查找blip元素获取r:embed属性
        const blipElement = cellImage.querySelector("a\\:blip, blip");
        const cNvPrElement = cellImage.querySelector("xdr\\:cNvPr, cNvPr");

        if (blipElement && cNvPrElement) {
          const rid = blipElement.getAttribute("r:embed");
          const imageId = cNvPrElement.getAttribute("name");

          if (rid && imageId) {
            // 🔧 关键修复：支持一个rid对应多个imageId
            if (!cellimagesNameRidMap[rid]) {
              cellimagesNameRidMap[rid] = [];
            }
            cellimagesNameRidMap[rid].push(imageId);
          }
        }
      } catch (e) {
        // 忽略解析错误
      }
    });

    // 关联媒体资源
    const relationshipElements =
      cellimagesRelsDoc.querySelectorAll("Relationship");

    relationshipElements.forEach((relationship) => {
      try {
        const target = relationship.getAttribute("Target");
        const id = relationship.getAttribute("Id");

        if (target && id) {
          const data = target.match(
            /media\/([a-zA-Z0-9]+[.][a-zA-Z0-9]{3,4})$/
          );
          if (!data) return;

          const fullName = data[1];
          const [name, extension] = fullName.split(".");
          const imageIds = cellimagesNameRidMap[id]; // 现在是数组

          if (imageIds && Array.isArray(imageIds)) {
            // 🔧 关键修复：为每个imageId都建立映射关系
            imageIds.forEach((imageId) => {
              // 在workbook.media中查找对应的媒体资源
              const mediaResource = workbook.media.find((v) => v.name === name);
              if (mediaResource) {
                result[imageId] = mediaResource;
              }
            });
          }
        }
      } catch (e) {
        // 忽略关联错误
      }
    });

    return result;
  } catch (error) {
    return null;
  }
};

// ✅ 处理富文本内容，只对optionsList保留格式，其他内容提取纯文本
const extractTextFromRichText = (cellValue, preserveRichText = false) => {
  // 如果是简单字符串，直接返回
  if (typeof cellValue === "string") {
    return cellValue;
  }

  // 如果是数字或其他基本类型，转换为字符串
  if (typeof cellValue === "number" || typeof cellValue === "boolean") {
    return String(cellValue);
  }

  // 如果是富文本对象
  if (cellValue && typeof cellValue === "object" && cellValue.richText) {
    if (preserveRichText) {
      // 保留富文本格式
      const richTextResult = convertToRichTextFormat(cellValue.richText);
      return richTextResult;
    } else {
      // 只提取纯文本
      return cellValue.richText.map((segment) => segment.text || "").join("");
    }
  }

  // 如果是其他对象类型，尝试提取text属性
  if (cellValue && typeof cellValue === "object" && cellValue.text) {
    return cellValue.text;
  }

  // 其他情况返回空字符串
  return "";
};

// ✅ 将Excel富文本转换为HTML富文本格式
const convertToRichTextFormat = (richTextArray) => {
  if (!Array.isArray(richTextArray)) {
    console.log("富文本数组无效:", richTextArray);
    return "";
  }

  console.log("处理富文本数组:", richTextArray);
  let htmlContent = "";

  richTextArray.forEach((segment, index) => {
    let text = segment.text || "";
    let styles = [];
    console.log(`处理富文本片段 ${index}:`, segment);

    // 如果文本为空，跳过这个片段
    if (!text) {
      return;
    }

    // 处理字体样式
    if (segment.font) {
      const font = segment.font;

      // 只保留字体颜色，去掉其他样式
      if (font.color) {
        console.log("检测到字体颜色:", font.color);
        let color = null;
        if (font.color.argb) {
          color = convertArgbToHex(font.color.argb);
          console.log("ARGB颜色转换:", font.color.argb, "->", color);
        } else if (font.color.rgb) {
          color = convertArgbToHex(font.color.rgb);
          console.log("RGB颜色转换:", font.color.rgb, "->", color);
        } else if (font.color.theme !== undefined) {
          // 主题颜色，使用默认颜色
          color = "#000000";
          console.log("主题颜色，使用默认黑色");
        }
        if (color && color !== "#000000") {
          // 只有非黑色才添加颜色样式
          styles.push(`color: ${color}`);
        }
      }
    }

    // 如果有样式，包装在span标签中
    if (styles.length > 0) {
      const styleAttr = styles.join("; ");
      const spanTag = `<span style="${styleAttr}">${escapeHtml(text)}</span>`;
      console.log("生成带样式的HTML:", spanTag);
      htmlContent += spanTag;
    } else {
      // 对于没有样式的文本，也要确保它是完整的
      const plainText = escapeHtml(text);
      console.log("生成纯文本:", plainText);
      htmlContent += plainText;
    }
  });

  console.log("最终HTML内容:", htmlContent);

  // 确保HTML标签是完整的，修复不完整的标签
  htmlContent = fixIncompleteHtmlTags(htmlContent);

  return htmlContent;
};

// ✅ 将ARGB颜色转换为十六进制颜色
const convertArgbToHex = (colorValue) => {
  if (!colorValue) {
    return "#000000";
  }

  let colorStr = String(colorValue);

  // 移除可能的前缀
  colorStr = colorStr.replace(/^(#|0x)/i, "");

  // ARGB格式: AARRGGBB，我们只需要RRGGBB部分
  if (colorStr.length === 8) {
    return "#" + colorStr.substring(2).toUpperCase();
  } else if (colorStr.length === 6) {
    return "#" + colorStr.toUpperCase();
  } else if (colorStr.length === 3) {
    // 短格式 RGB，扩展为完整格式
    return (
      "#" +
      colorStr
        .split("")
        .map((c) => c + c)
        .join("")
        .toUpperCase()
    );
  }

  return "#000000";
};

// ✅ HTML转义函数
const escapeHtml = (text) => {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
};

// ✅ 修复不完整的HTML标签
const fixIncompleteHtmlTags = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== "string") {
    return htmlContent;
  }

  // 查找所有未闭合的span标签
  const openTags = [];
  const tagPattern = /<\/?span[^>]*>/g;
  let match;

  while ((match = tagPattern.exec(htmlContent)) !== null) {
    const tag = match[0];
    if (tag.startsWith("</")) {
      // 闭合标签
      if (openTags.length > 0) {
        openTags.pop();
      }
    } else if (!tag.endsWith("/>")) {
      // 开始标签（非自闭合）
      openTags.push(tag);
    }
  }

  // 为所有未闭合的标签添加闭合标签
  let fixedContent = htmlContent;
  for (let i = openTags.length - 1; i >= 0; i--) {
    fixedContent += "</span>";
  }

  console.log("修复HTML标签:", htmlContent, "->", fixedContent);
  return fixedContent;
};

// ✅ 专门处理选项富文本的函数
const processOptionsRichText = (richTextContent) => {
  if (!richTextContent || typeof richTextContent !== "string") {
    return [];
  }

  console.log("=== 开始处理选项富文本 ===");
  console.log("原始富文本内容:", richTextContent);
  console.log("内容长度:", richTextContent.length);
  console.log(
    "内容字符详情:",
    richTextContent
      .split("")
      .map((char, i) => `${i}: '${char}' (${char.charCodeAt(0)})`)
  );

  // 首先尝试智能分割，保持HTML标签完整
  const optionsList = [];

  // 新方法：使用更直接的分割方式
  // 先尝试按选项前缀分割，但保持HTML标签完整

  // 方法1: 尝试多种分割模式
  let parts = [];

  // 尝试不同的分割模式（支持冒号、中文冒号、顿号）
  const patterns = [
    /\n(?=[A-Z][：:、]\s*)/g, // 换行后跟选项前缀（支持多种分隔符）
    /(?<=\n)[A-Z][：:、]\s*/g, // 选项前缀本身
    /[A-Z][：:、]\s*(?=.*)/g, // 任何位置的选项前缀
  ];

  for (const pattern of patterns) {
    parts = richTextContent.split(pattern);
    console.log(`使用模式 ${pattern} 分割结果:`, parts);

    if (parts.length > 1) {
      // 找到了有效的分割
      break;
    }
  }

  // 如果还是没有分割成功，尝试手动查找
  if (parts.length <= 1) {
    console.log("自动分割失败，尝试手动查找选项");

    // 手动查找所有选项位置（支持多种分隔符）
    const optionPositions = [];
    const optionRegex = /[A-Z][：:、]\s*/g;
    let match;

    while ((match = optionRegex.exec(richTextContent)) !== null) {
      optionPositions.push({
        index: match.index,
        prefix: match[0],
        length: match[0].length,
      });
      console.log(`找到选项前缀 "${match[0]}" 在位置 ${match.index}`);
    }

    // 根据位置手动分割
    if (optionPositions.length > 0) {
      parts = [];
      for (let i = 0; i < optionPositions.length; i++) {
        const current = optionPositions[i];
        const next = optionPositions[i + 1];

        const start = current.index + current.length;
        const end = next ? next.index : richTextContent.length;

        const content = richTextContent.substring(start, end).trim();
        if (content) {
          parts.push(content);
          console.log(`手动提取选项 ${i + 1}:`, content);
        }
      }
    }
  }

  console.log("最终分割后的部分:", parts);

  parts.forEach((part, index) => {
    const trimmedPart = part.trim();
    if (trimmedPart) {
      // 检查这个部分是否包含多个选项（支持多种分隔符）
      const hasMultipleOptions = /\n.*[A-Z][：:、]/.test(trimmedPart);

      if (hasMultipleOptions) {
        console.log(`检测到部分 ${index + 1} 包含多个选项:`, trimmedPart);

        // 进一步分割这个部分（支持多种分隔符）
        const subParts = trimmedPart.split(/\n(?=.*[A-Z][：:、])/);
        console.log(`子分割结果:`, subParts);

        subParts.forEach((subPart, subIndex) => {
          let cleanSubOption = subPart
            .replace(/^[A-Z][：:、]\s*/, "") // 移除开头的选项前缀
            .replace(/【答案】/g, "")
            .trim();

          console.log(`初步清理后:`, cleanSubOption);

          // 特殊处理：如果选项前缀在HTML标签内（如 <span>C：政子</span>）
          // 使用更精确的正则表达式移除HTML标签内的选项前缀
          cleanSubOption = cleanSubOption.replace(/>[A-Z][：:、]/g, ">");
          console.log(`移除HTML标签内的选项前缀后:`, cleanSubOption);

          if (cleanSubOption) {
            optionsList.push(cleanSubOption);
            console.log(
              `最终选项 ${index + 1}.${subIndex + 1}:`,
              cleanSubOption
            );
          }
        });
      } else {
        // 单个选项的正常处理（支持多种分隔符）
        const cleanOption = trimmedPart
          .replace(/^[A-Z][：:、]\s*/, "")
          .replace(/【答案】/g, "")
          .replace(/\n$/, "") // 移除末尾换行符
          .trim();

        if (cleanOption) {
          optionsList.push(cleanOption);
          console.log(`最终选项 ${index + 1}:`, cleanOption);
        }
      }
    }
  });

  // 如果上面的方法没有成功分割，使用备用方法
  if (optionsList.length === 0) {
    console.log("使用备用分割方法");

    // 备用方法：逐字符分析，找到选项边界
    let currentOption = "";
    let i = 0;

    while (i < richTextContent.length) {
      const char = richTextContent[i];

      // 检查是否是选项前缀的开始
      if (char === "\n" && i + 1 < richTextContent.length) {
        const nextChar = richTextContent[i + 1];
        if (/[A-Z]/.test(nextChar)) {
          // 可能是新选项的开始，检查后面是否有冒号
          const remaining = richTextContent.substring(i + 1);
          const prefixMatch = remaining.match(/^[A-Z][：:、]\s*/);

          if (prefixMatch) {
            // 确实是新选项，保存当前选项
            if (currentOption.trim()) {
              const cleanOption = currentOption
                .replace(/^[A-Z][：:、]\s*/, "")
                .replace(/【答案】/g, "")
                .trim();
              if (cleanOption) {
                optionsList.push(cleanOption);
                console.log(`备用方法选项 ${optionsList.length}:`, cleanOption);
              }
            }

            // 跳过换行符，开始新选项
            i++;
            currentOption = "";
            continue;
          }
        }
      }

      currentOption += char;
      i++;
    }

    // 处理最后一个选项
    if (currentOption.trim()) {
      const cleanOption = currentOption
        .replace(/^[A-Z][：:、]\s*/, "")
        .replace(/【答案】/g, "")
        .trim();
      if (cleanOption) {
        optionsList.push(cleanOption);
        console.log(`备用方法最后选项:`, cleanOption);
      }
    }
  }

  console.log("处理后的选项列表:", optionsList);
  return optionsList;
};

// ✅ 生成ArrayBuffer的简单哈希值，用于识别重复图片
const generateImageHash = (buffer) => {
  const bytes = new Uint8Array(buffer);
  let hash = 0;

  // 使用简单的哈希算法
  for (let i = 0; i < bytes.length; i++) {
    hash = ((hash << 5) - hash + bytes[i]) & 0xffffffff;
  }

  // 为了更好的唯一性，也考虑文件大小
  return `${hash}_${bytes.length}`;
};

// ✅ 将ArrayBuffer转换为File对象，可以直接上传
const arrayBufferToFile = (buffer, fileName, mimeType) => {
  const blob = new Blob([buffer], { type: mimeType });
  return new File([blob], fileName, { type: mimeType });
};

// ✅ 将ArrayBuffer转换为Blob对象，也可以上传
const arrayBufferToBlob = (buffer, mimeType) => {
  return new Blob([buffer], { type: mimeType });
};

// ✅ 并发控制的图片上传函数
const uploadImagesWithConcurrency = async (
  allImages,
  workbook,
  uploadedHashes,
  imageIdToUrlMap,
  uploadedCount,
  percentStep,
  startPercent,
  maxConcurrency = 6
) => {
  const semaphore = new Array(maxConcurrency).fill(null);
  let currentIndex = 0;

  const uploadSingleImage = async (image) => {
    try {
      // 检查是否已经上传过相同哈希的图片
      let imageUrl = uploadedHashes.get(image.hash);

      if (!imageUrl) {
        // 首次上传这个哈希的图片
        const fileName =
          image.media.name || `excel_image_${Date.now()}_${image.id}.png`;
        const fileObject = arrayBufferToFile(
          image.buffer,
          fileName,
          image.type
        );

        // 🔧 使用带重试的上传函数
        const uploadResult = await uploadImageToOSSWithRetry(
          fileObject,
          fileName,
          2
        );

        if (uploadResult?.success && uploadResult?.url) {
          imageUrl = uploadResult.url;
          uploadedHashes.set(image.hash, imageUrl);
        } else {
          // 🔧 上传失败时，标记为失败但不抛出异常，让题目被忽略
          console.error(
            `❌ 图片上传重试后仍失败，跳过题目: ${uploadResult?.error}`
          );
          imageUrl = null; // 标记为失败
        }
      }

      // 为每个图片的媒体索引单独映射URL
      const mediaResource = workbook.model.media.find((m) => m === image.media);
      if (mediaResource) {
        imageIdToUrlMap.set(mediaResource.index, imageUrl);
      }
    } catch (e) {
      console.error(`❌ 图片上传重试后仍异常，跳过题目:`, e);
      // imageUploadFailCountNum.value++; // 增加失败计数

      // 为这个特定的图片设置null
      const mediaResource = workbook.model.media.find((m) => m === image.media);
      if (mediaResource) {
        imageIdToUrlMap.set(mediaResource.index, null);
      }
    } finally {
      // 每上传一张图片，进度递增
      uploadedCount.value++;
      let percent = Math.floor(
        startPercent + uploadedCount.value * percentStep
      );
      if (percent > 60) percent = 60;
      progressPercent.value = percent;
    }
  };

  // 并发控制逻辑
  const workers = semaphore.map(async () => {
    while (currentIndex < allImages.length) {
      const imageIndex = currentIndex++;
      if (imageIndex < allImages.length) {
        await uploadSingleImage(allImages[imageIndex]);
      }
    }
  });

  await Promise.all(workers);
};

// ✅ 带重试机制的图片上传函数
const uploadImageToOSSWithRetry = async (
  fileObject,
  fileName,
  maxRetries = 2
) => {
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      // console.log(`🔄 图片上传尝试 ${attempt}/${maxRetries + 1}: ${fileName}`);
      const result = await uploadImageToOSS(fileObject, fileName);

      if (result?.success && result?.url) {
        // console.log(`✅ 图片上传成功 (尝试${attempt}次): ${fileName}`);
        return result;
      } else {
        // console.warn(
        //   `⚠️ 图片上传失败 (尝试${attempt}次): ${fileName}, 错误: ${result?.error}`
        // );
        if (attempt === maxRetries + 1) {
          return {
            success: false,
            error: result?.error || "上传失败",
            fileName,
          };
        }
        // 等待一段时间后重试
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    } catch (error) {
      // console.error(`❌ 图片上传异常 (尝试${attempt}次): ${fileName}`, error);
      if (attempt === maxRetries + 1) {
        return { success: false, error: error.message, fileName };
      }
      // 等待一段时间后重试
      await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
    }
  }
};

// ✅ 上传图片到OSS的函数 - 参考upload.vue的实现
const uploadImageToOSS = async (fileObject, fileName) => {
  try {
    // 1. 获取STS Token，参考upload.vue的beforeUpload方法
    const response = await stsTokenApi();

    const ext = fileName.split(".").pop();
    const baseFileName = response.data.fileName;

    // 2. 构建OSS上传参数，参考upload.vue的dataObj
    const dataObj = {
      policy: response.data.policy,
      signature: response.data.signature,
      key: response.data.dir + `/${baseFileName}.${ext}`,
      ossaccessKeyId: response.data.accessKeyId,
      dir: response.data.dir,
      host: response.data.host,
      success_action_status: "200",
    };

    // 3. 构建FormData，参考upload.vue的customUpload方法
    const formData = new FormData();

    // 添加OSS必需的字段
    Object.keys(dataObj).forEach((key) => {
      if (dataObj[key]) {
        formData.append(key, dataObj[key]);
      }
    });

    // 添加文件，注意file字段要放在最后
    formData.append("file", fileObject);

    // 4. 发送请求到OSS，参考upload.vue的ossUploadUrl
    const ossUploadUrl = "https://images2.kkzhw.com";
    const uploadResponse = await fetch(ossUploadUrl, {
      method: "POST",
      body: formData,
    });

    if (uploadResponse.ok) {
      // 5. 构建完整的URL，参考upload.vue的handleUploadSuccess
      const fullUrl = `${dataObj.host}/${dataObj.key}`;

      return {
        success: true,
        url: fullUrl,
        fileName: fileName,
        originalResponse: {
          dataObj: dataObj,
          status: uploadResponse.status,
        },
      };
    } else {
      // 不抛出异常，而是返回失败结果
      const errorText = await uploadResponse.text().catch(() => "未知错误");
      return {
        success: false,
        error: `上传失败: ${uploadResponse.status} ${uploadResponse.statusText}`,
        fileName: fileName,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      fileName: fileName,
    };
  }
};

// ✅ 上传图片到服务器的示例函数
const uploadImageToServer = async (imageInfo, uploadUrl) => {
  try {
    const formData = new FormData();

    // 方式1：使用File对象上传
    formData.append("file", imageInfo.file);

    // 方式2：使用Blob对象上传（如果需要自定义文件名）
    // formData.append('file', imageInfo.blob, imageInfo.name);

    // 添加其他参数
    formData.append("fileName", imageInfo.name);
    formData.append("mimeType", imageInfo.type);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      // console.log("✅ 图片上传成功:", result);
      return result;
    } else {
      throw new Error(`上传失败: ${response.status}`);
    }
  } catch (error) {
    // console.error("❌ 图片上传失败:", error);
    throw error;
  }
};

// ✅ 批量上传所有图片的示例函数
const uploadAllImages = async (parsedSheets, uploadUrl) => {
  const uploadResults = [];

  for (const sheet of parsedSheets) {
    for (const row of sheet.rows) {
      for (const cell of row) {
        if (cell.type === "image" && cell.imageInfo) {
          try {
            const result = await uploadImageToServer(cell.imageInfo, uploadUrl);
            uploadResults.push({
              position: cell.position,
              uploadResult: result,
            });
          } catch (error) {
            uploadResults.push({
              position: cell.position,
              error: error.message,
            });
          }
        }
      }
    }
  }

  return uploadResults;
};

// 组件挂载时获取题库选项
onMounted(() => {
  getGameCategoryOptions();
});
</script>

<style scoped>
/* 样式保持不变 */
.cell-image {
  max-width: 120px;
  max-height: 100px;
  display: block;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.image-error {
  color: #e74c3c;
  font-size: 0.9rem;
  padding: 8px;
  background: #ffeaea;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* 题库选择器样式 */
.category-select-wrapper {
  position: relative;
}

.export-btn {
  background: #28a745;
}

.export-btn:hover {
  background: #218838;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  color: #007bff;
  font-size: 1.1rem;
}

.debug-info p {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.json-preview {
  margin-top: 20px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.json-preview h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
}

.json-preview pre {
  background: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 0.8rem;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.converted-data-preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.converted-data-preview h3 {
  color: #28a745;
  border-bottom-color: #28a745;
}

.excel-viewer {
  max-width: 800px;
  margin: 0 auto;
}

.error {
  color: #e74c3c;
  background: #fdeded;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-weight: 500;
}
.successText {
  color: #04d023;
  background: #d3f7d7;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-weight: 500;
}
.validation-errors {
  margin: 20px 0;
}

.validation-errors h3 {
  color: #e74c3c !important;
  border-bottom-color: #e74c3c !important;
  margin-bottom: 15px;
}

.sheet-tabs {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.sheet-tabs button {
  padding: 8px 16px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.sheet-tabs button.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.sheet-content {
  margin-top: 20px;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.table-container {
  overflow-x: auto;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

th {
  background: #3498db;
  color: white;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
}

tr:nth-child(even) {
  background-color: #f8f9fa;
}

tr:hover {
  background-color: #e3f2fd;
}

/* 文件上传样式 */
.file-upload-wrapper {
  position: relative;
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.95);
  color: #4a5568;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 200px;
  justify-content: center;
}

.file-upload-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.file-upload-btn.disabled {
  background: rgba(255, 255, 255, 0.5);
  color: #eaeaea;
  border-color: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.upload-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* 解析按钮样式 */
.parse-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
  min-width: 160px;
  justify-content: center;
}

.parse-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
}

.parse-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #cbd5e0 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.parse-btn.loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.parse-icon,
.btn-spinner {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.btn-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading 指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 20px 0;
  color: #495057;
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

